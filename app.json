{"expo": {"name": "<PERSON><PERSON>anner", "slug": "racine-mode-scanner", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "racinemode", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.racinemode.scanner", "permissions": ["CAMERA", "INTERNET"], "versionCode": 1}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": [["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], "expo-router", "expo-camera", "expo-font"], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "9eb475a4-48d9-4754-9d8e-48069026cd40"}}}}