import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator
} from 'react-native';
import { Camera, CameraView, useCameraPermissions, BarcodeScanningResult } from 'expo-camera';
import { router } from 'expo-router';
import { ArrowLeft, ZapOff } from 'lucide-react-native';

import { ScanType } from '@/types';
import Button from '@/components/ui/Button';

interface BarcodeScannerProps {
  onScan: (barcode: string) => void;
  scanType: ScanType;
  isProcessing?: boolean;
}

export default function BarcodeScanner({
  onScan,
  scanType,
  isProcessing = false
}: BarcodeScannerProps) {
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [torch, setTorch] = useState(false);

  useEffect(() => {
    // Reset scanned state when processing is complete
    if (!isProcessing) {
      setScanned(false);
    }
  }, [isProcessing]);

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.text}>Loading camera...</Text>
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions not granted yet
    return (
      <View style={styles.permissionContainer}>
        <ZapOff size={48} color="#64748b" />
        <Text style={styles.permissionText}>
          We need permission to use the camera to scan barcodes
        </Text>
        <Button
          title="Grant Permission"
          onPress={requestPermission}
          style={styles.permissionButton}
        />
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.cancelButton}
        >
          <Text style={styles.cancelText}>Cancel</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleBarCodeScanned = ({ data }: BarcodeScanningResult) => {
    if (scanned || isProcessing) return;

    setScanned(true);
    onScan(data);
  };

  const toggleTorch = () => {
    setTorch(prev => !prev);
  };

  const getHeaderTitle = () => {
    switch (scanType) {
      case 'general':
        return 'General Scan';
      case 'order':
        return 'Order de Fabrication';
      case 'packet':
        return 'Packet Scan';
      default:
        return 'Scan Barcode';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <ArrowLeft color="#ffffff" size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>{getHeaderTitle()}</Text>
        <View style={styles.placeholder} />
      </View>

      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          onBarcodeScanned={scanned || isProcessing ? undefined : handleBarCodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: ['qr', 'code128', 'code39', 'ean13', 'ean8'],
          }}

        >
          <View style={styles.overlay}>
            <View style={styles.scanArea}>
              {(scanned || isProcessing) && (
                <View style={styles.scanningOverlay}>
                  {isProcessing && (
                    <ActivityIndicator size="large" color="#ffffff" />
                  )}
                  <Text style={styles.scanningText}>
                    {isProcessing ? 'Processing scan...' : 'Scan complete'}
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.scanInstructions}>
              <Text style={styles.instructionText}>
                Align barcode within the frame
              </Text>
            </View>
          </View>
        </CameraView>
      </View>

      <View style={styles.controls}>
        <Button
          title={torch ? 'Turn Off Light' : 'Turn On Light'}
          onPress={toggleTorch}
          variant="outline"
          style={styles.torchButton}
        />

        <Button
          title="Cancel"
          onPress={() => router.back()}
          variant="secondary"
          style={styles.button}
        />

        <Button
          title="Scan Again"
          onPress={() => setScanned(false)}
          style={styles.button}
          disabled={!scanned || isProcessing}
        />
      </View>
    </View>
  );
}

const { width } = Dimensions.get('window');
const scanAreaSize = width * 0.7;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f172a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 48,
    paddingBottom: 16,
    backgroundColor: '#1e293b',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  placeholder: {
    width: 40,
  },
  cameraContainer: {
    flex: 1,
    alignItems: 'center',     // center horizontally
    justifyContent: 'flex-start', // align items from top
    paddingTop: 60,           // push it down from top
    backgroundColor: '#0f172a',
  },
  camera: {
    width: '80%',
    aspectRatio: 1, // Makes it a square
    borderRadius: 16,
    overflow: 'hidden',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: scanAreaSize,
    height: scanAreaSize,
    borderWidth: 2,
    borderColor: '#3b82f6',
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  scanningOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(59,130,246,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
    marginTop: 16,
  },
  scanInstructions: {
    padding: 16,
    marginTop: 16,
    backgroundColor: 'rgba(15,23,42,0.7)',
    borderRadius: 8,
  },
  instructionText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  controls: {
    padding: 16,
    backgroundColor: '#1e293b',
  },
  button: {
    marginTop: 8,
  },
  torchButton: {
    marginBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  text: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f8fafc',
  },
  permissionText: {
    fontSize: 16,
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
    color: '#334155',
  },
  permissionButton: {
    width: '100%',
    marginBottom: 16,
  },
  cancelButton: {
    padding: 16,
  },
  cancelText: {
    color: '#64748b',
    fontSize: 16,
  },
});