import React from 'react';
import { Redirect } from 'expo-router';
import { useAuth } from '@/context/AuthContext';
import { UserRole } from '@/types';

interface RoleBasedRouteProps {
  children: React.ReactNode;
  allowedRoles: UserRole[];
  fallbackPath?: string;
}

/**
 * A component that restricts access to routes based on user roles.
 * If the user's role is not in the allowedRoles array, they will be redirected to the fallbackPath.
 */
export default function RoleBasedRoute({
  children,
  allowedRoles,
  fallbackPath = '/',
}: RoleBasedRouteProps) {
  const { user } = useAuth();

  // If user has no role or their role is not in the allowed roles, redirect
  if (!user?.role || !allowedRoles.includes(user.role)) {
    return <Redirect href={fallbackPath} />;
  }

  // Otherwise, render the children
  return <>{children}</>;
}
