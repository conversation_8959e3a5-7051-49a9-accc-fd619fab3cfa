import React from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Switch,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LogOut, User, Settings, Bell, Info as InfoIcon, Sun, Moon, Shield } from 'lucide-react-native';

import { useAuth } from '@/context/AuthContext';
import Card from '@/components/ui/Card';

// Helper function to format role names for display
const formatRoleName = (role: string): string => {
  switch (role) {
    case 'admin':
      return 'Administrateur';
    case 'responsable_chaine':
      return 'Responsable Chaîne';
    case 'controlleur_fin_chaine':
      return 'Contrôleur Fin de Chaîne';
    case 'ouvrier_machine':
      return 'Ouvrier Machine';
    case 'ouvrier_finnition':
      return 'Ouvrier Finition';
    default:
      return role.charAt(0).toUpperCase() + role.slice(1).replace('_', ' ');
  }
};

export default function ProfileScreen() {
  const { user, signOut } = useAuth();
  const [darkMode, setDarkMode] = React.useState(false);
  const [notifications, setNotifications] = React.useState(true);

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Sign Out',
          onPress: signOut,
          style: 'destructive',
        },
      ],
    );
  };

  const toggleDarkMode = () => {
    setDarkMode(prev => !prev);
  };

  const toggleNotifications = () => {
    setNotifications(prev => !prev);
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.profileSection}>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>
              {user?.username?.charAt(0).toUpperCase() || 'U'}
            </Text>
          </View>
          <Text style={styles.userName}>
            {user?.username || 'User'}
          </Text>
          <Text style={styles.userEmail}>
            {user?.email || '<EMAIL>'}
          </Text>
          <View style={styles.roleContainer}>
            <Shield size={16} color="#3b82f6" />
            <Text style={styles.roleText}>
              {user?.role ? formatRoleName(user.role) : 'Admin'}
            </Text>
          </View>
        </View>

        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Account</Text>

          <Card style={styles.settingsCard}>
            <TouchableOpacity style={styles.settingItem}>
              <View style={styles.settingIcon}>
                <User size={20} color="#3b82f6" />
              </View>
              <Text style={styles.settingText}>Edit Profile</Text>
            </TouchableOpacity>

            <View style={styles.separator} />

            <TouchableOpacity style={styles.settingItem}>
              <View style={styles.settingIcon}>
                <Settings size={20} color="#3b82f6" />
              </View>
              <Text style={styles.settingText}>Account Settings</Text>
            </TouchableOpacity>
          </Card>

          <Text style={styles.sectionTitle}>Preferences</Text>

          <Card style={styles.settingsCard}>
            <View style={styles.settingSwitchItem}>
              <View style={styles.settingSwitchLeft}>
                <View style={styles.settingIcon}>
                  {darkMode ? (
                    <Moon size={20} color="#8b5cf6" />
                  ) : (
                    <Sun size={20} color="#f59e0b" />
                  )}
                </View>
                <Text style={styles.settingText}>Dark Mode</Text>
              </View>
              <Switch
                value={darkMode}
                onValueChange={toggleDarkMode}
                trackColor={{ false: '#e2e8f0', true: '#bfdbfe' }}
                thumbColor={darkMode ? '#3b82f6' : '#ffffff'}
              />
            </View>

            <View style={styles.separator} />

            <View style={styles.settingSwitchItem}>
              <View style={styles.settingSwitchLeft}>
                <View style={styles.settingIcon}>
                  <Bell size={20} color="#3b82f6" />
                </View>
                <Text style={styles.settingText}>Notifications</Text>
              </View>
              <Switch
                value={notifications}
                onValueChange={toggleNotifications}
                trackColor={{ false: '#e2e8f0', true: '#bfdbfe' }}
                thumbColor={notifications ? '#3b82f6' : '#ffffff'}
              />
            </View>
          </Card>

          <Text style={styles.sectionTitle}>About</Text>

          <Card style={styles.settingsCard}>
            <TouchableOpacity style={styles.settingItem}>
              <View style={styles.settingIcon}>
                <InfoIcon size={20} color="#3b82f6" />
              </View>
              <Text style={styles.settingText}>About App</Text>
            </TouchableOpacity>
          </Card>

          <TouchableOpacity
            style={styles.signOutButton}
            onPress={handleSignOut}
          >
            <LogOut size={20} color="#ef4444" />
            <Text style={styles.signOutText}>Sign Out</Text>
          </TouchableOpacity>

          <Text style={styles.versionText}>Version 1.0.0</Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
  },
  content: {
    flex: 1,
  },
  profileSection: {
    alignItems: 'center',
    paddingVertical: 24,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 32,
    fontWeight: '600',
  },
  userName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 8,
  },
  roleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e0f2fe',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  roleText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#0284c7',
    marginLeft: 6,
  },
  settingsSection: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#64748b',
    marginBottom: 8,
    marginTop: 16,
  },
  settingsCard: {
    padding: 0,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  settingSwitchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  settingSwitchLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    fontSize: 16,
    color: '#1e293b',
  },
  separator: {
    height: 1,
    backgroundColor: '#e2e8f0',
    marginLeft: 16,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    marginTop: 24,
    backgroundColor: '#fef2f2',
    borderRadius: 8,
  },
  signOutText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#ef4444',
    marginLeft: 8,
  },
  versionText: {
    fontSize: 12,
    color: '#94a3b8',
    textAlign: 'center',
    marginTop: 16,
  },
});

