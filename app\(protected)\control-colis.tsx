import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Alert
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, CircleCheck, CircleAlert } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';

import { useAuth } from '@/context/AuthContext';
import { apiRequest } from '@/utils/api';
import { playBeepSound } from '@/utils/sound';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

interface Piece {
  _id: string;
  reference: string;
  status?: string;
  defaut?: string[];
}

interface Packet {
  _id: string;
  qrCode: string;
  status: string;
  pieces: Piece[];
}

interface ColisData {
  _id: string;
  numeroColis: number;
  coloris: string;
  tailles: string;
  quantite: number;
  status: string;
  problems: string[];
  control?: 'Conforme' | 'ANC' | 'Bloque';
  order: {
    orderNumber: string;
    status: string;
  };
  packets: Packet[];
  qrCode: string;
}

interface ControlState {
  control: 'Conforme' | 'ANC' | 'Bloque' | null;
  problems: string[];
}

export default function ControlColisScreen() {
  const { user } = useAuth();
  const { colisId } = useLocalSearchParams<{ colisId: string }>();
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [colis, setColis] = useState<ColisData | null>(null);
  const [controlState, setControlState] = useState<ControlState>({
    control: null,
    problems: []
  });
  const [error, setError] = useState<string | null>(null);

  const AVAILABLE_PROBLEMS = ['Nuance', 'Symetrie', 'Mesure', 'Autre'];

  useEffect(() => {
    fetchColisData();
  }, [colisId]);

  const fetchColisData = async () => {
    if (!colisId) return;

    try {
      setIsLoading(true);
      // Since we have the colisId, we need to fetch the colis data
      // We'll use a direct API call to get colis by ID
      const response = await apiRequest<ColisData>(`/colis/${colisId}`, {
        method: 'GET',
      });

      if (response.success && response.data) {
        setColis(response.data);

        // Initialize control state with existing data if available
        setControlState({
          control: response.data.control || null,
          problems: response.data.problems || []
        });
      } else {
        setError(response.error || 'Failed to load colis data');
      }
    } catch (err) {
      console.error('Error fetching colis data:', err);
      setError('Failed to load colis data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleControlChange = (control: 'Conforme' | 'ANC' | 'Bloque') => {
    setControlState(prev => ({
      ...prev,
      control,
      // Reset problems if selecting "Conforme"
      problems: control === 'Conforme' ? [] : prev.problems
    }));
  };

  const handleProblemToggle = (problem: string) => {
    setControlState(prev => ({
      ...prev,
      problems: prev.problems.includes(problem)
        ? prev.problems.filter(p => p !== problem)
        : [...prev.problems, problem]
    }));
  };

  const isNonConforme = () => {
    return controlState.control === 'ANC' || controlState.control === 'Bloque';
  };

  const handleSubmit = async () => {
    if (!colis || !controlState.control) {
      Alert.alert('Erreur', 'Veuillez sélectionner un état de conformité.');
      return;
    }

    // Validate that problems are selected for non-conforme items
    if (isNonConforme() && controlState.problems.length === 0) {
      Alert.alert('Erreur', 'Veuillez sélectionner au moins un problème pour les articles non conformes.');
      return;
    }

    setIsProcessing(true);

    try {
      console.log('Submitting colis control:', {
        colisId: colis._id,
        control: controlState.control,
        problems: controlState.problems
      });

      const response = await apiRequest('/colis/handle-colis-defects', {
        method: 'POST',
        body: {
          colisId: colis._id,
          control: controlState.control,
          problems: controlState.problems,
        },
      });

      if (response.success) {
        // Trigger success haptic feedback
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Play beep sound
        playBeepSound();

        // Update the colis data with the new control state
        if (response.data && response.data.colis) {
          setColis(response.data.colis);
          setControlState({
            control: response.data.colis.control || null,
            problems: response.data.colis.problems || []
          });
        }

        Alert.alert(
          'Contrôle terminé',
          'Le contrôle du colis a été enregistré avec succès.',
          [
            {
              text: 'Nouveau Scan',
              onPress: () => router.push('/scan/colis')
            },
            {
              text: 'Retour',
              onPress: () => router.push('/(protected)/(tabs)/')
            }
          ]
        );
      } else {
        Alert.alert(
          'Erreur',
          response.error || 'Échec de l\'enregistrement du contrôle'
        );
      }
    } catch (error) {
      console.error('Error submitting control:', error);
      Alert.alert(
        'Erreur',
        'Une erreur est survenue lors de l\'enregistrement. Veuillez réessayer.'
      );
    } finally {
      setIsProcessing(false);
    }
  };



  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#8b5cf6" />
        <Text style={styles.loadingText}>Chargement du colis...</Text>
      </View>
    );
  }

  if (error || !colis) {
    return (
      <View style={styles.errorContainer}>
        <CircleAlert size={64} color="#ef4444" />
        <Text style={styles.errorMessage}>{error || 'Colis non trouvé'}</Text>
        <Button
          title="Retour"
          onPress={handleGoBack}
          variant="secondary"
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={handleGoBack}
          style={styles.backButton}
        >
          <ArrowLeft color="#ffffff" size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Contrôle Colis</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        <Card style={styles.colisCard}>
          <Text style={styles.colisTitle}>Colis N°{colis.numeroColis}</Text>
          <Text style={styles.colisDetail}>OF: {colis.order.orderNumber}</Text>
          <Text style={styles.colisDetail}>Coloris: {colis.coloris}</Text>
          <Text style={styles.colisDetail}>Tailles: {colis.tailles}</Text>
          <Text style={styles.colisDetail}>Quantité: {colis.quantite}</Text>
          <Text style={styles.colisDetail}>Total Paquets: {colis.packets.length}</Text>
        </Card>

        {/* Control State Selection */}
        <Card style={styles.controlCard}>
          <Text style={styles.controlTitle}>État de Conformité</Text>

          <View style={styles.controlOptions}>
            <TouchableOpacity
              style={[
                styles.controlOption,
                controlState.control === 'Conforme' && styles.controlOptionSelected
              ]}
              onPress={() => handleControlChange('Conforme')}
            >
              <Text style={[
                styles.controlOptionText,
                controlState.control === 'Conforme' && styles.controlOptionTextSelected
              ]}>
                Conforme
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.controlOption,
                controlState.control === 'ANC' && styles.controlOptionSelected
              ]}
              onPress={() => handleControlChange('ANC')}
            >
              <Text style={[
                styles.controlOptionText,
                controlState.control === 'ANC' && styles.controlOptionTextSelected
              ]}>
                Envoie ANC Client
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.controlOption,
                controlState.control === 'Bloque' && styles.controlOptionSelected
              ]}
              onPress={() => handleControlChange('Bloque')}
            >
              <Text style={[
                styles.controlOptionText,
                controlState.control === 'Bloque' && styles.controlOptionTextSelected
              ]}>
                Bloqué
              </Text>
            </TouchableOpacity>
          </View>
        </Card>

        {/* Problems Selection - Only show if non-conforme */}
        {isNonConforme() && (
          <Card style={styles.problemsCard}>
            <Text style={styles.problemsTitle}>Problèmes Identifiés</Text>
            <Text style={styles.problemsSubtitle}>
              Sélectionnez un ou plusieurs problèmes:
            </Text>

            <View style={styles.problemsList}>
              {AVAILABLE_PROBLEMS.map((problem) => (
                <TouchableOpacity
                  key={problem}
                  style={[
                    styles.problemOption,
                    controlState.problems.includes(problem) && styles.problemOptionSelected
                  ]}
                  onPress={() => handleProblemToggle(problem)}
                >
                  <View style={[
                    styles.checkbox,
                    controlState.problems.includes(problem) && styles.checkboxSelected
                  ]}>
                    {controlState.problems.includes(problem) && (
                      <CircleCheck size={16} color="#ffffff" />
                    )}
                  </View>
                  <Text style={[
                    styles.problemText,
                    controlState.problems.includes(problem) && styles.problemTextSelected
                  ]}>
                    {problem}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card>
        )}

        <View style={styles.submitContainer}>
          <Button
            title={isProcessing ? 'Enregistrement...' : 'Enregistrer le contrôle'}
            onPress={handleSubmit}
            isLoading={isProcessing}
            style={styles.submitButton}
            disabled={isProcessing}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#8b5cf6',
    paddingTop: 50,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  errorMessage: {
    fontSize: 18,
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
  colisCard: {
    marginBottom: 16,
    padding: 16,
  },
  colisTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
  },
  colisDetail: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 4,
  },
  controlCard: {
    marginBottom: 16,
    padding: 16,
  },
  controlTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
  },
  controlOptions: {
    gap: 12,
  },
  controlOption: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#e2e8f0',
    backgroundColor: '#ffffff',
    alignItems: 'center',
  },
  controlOptionSelected: {
    borderColor: '#8b5cf6',
    backgroundColor: '#f3f4f6',
  },
  controlOptionText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#64748b',
  },
  controlOptionTextSelected: {
    color: '#8b5cf6',
    fontWeight: '600',
  },
  problemsCard: {
    marginBottom: 16,
    padding: 16,
  },
  problemsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 8,
  },
  problemsSubtitle: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 16,
  },
  problemsList: {
    gap: 12,
  },
  problemOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    backgroundColor: '#ffffff',
    gap: 12,
  },
  problemOptionSelected: {
    borderColor: '#8b5cf6',
    backgroundColor: '#f3f4f6',
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#d1d5db',
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxSelected: {
    borderColor: '#8b5cf6',
    backgroundColor: '#8b5cf6',
  },
  problemText: {
    fontSize: 16,
    color: '#64748b',
    flex: 1,
  },
  problemTextSelected: {
    color: '#8b5cf6',
    fontWeight: '500',
  },
  submitContainer: {
    marginTop: 20,
    marginBottom: 40,
  },
  submitButton: {
    backgroundColor: '#ffa600',
    borderRadius: 12,
    padding: 16,
  },
});
