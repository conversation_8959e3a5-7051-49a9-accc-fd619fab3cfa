import React from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { CheckCircle, ScanLine } from 'lucide-react-native';
import { useAuth } from '@/context/AuthContext';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import RoleBasedRoute from '@/components/auth/RoleBasedRoute';

export default function ControlFinChaineScreen() {
  const { user } = useAuth();
  
  const handleScanPress = () => {
    router.push('/scan/ctrl-fin-ch');
  };

  return (
    <RoleBasedRoute allowedRoles={['admin', 'controlleur_fin_chaine']}>
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Contrôle Fin de Chaîne</Text>
        </View>
        
        <ScrollView style={styles.content}>
          <View style={styles.introSection}>
            <Text style={styles.introTitle}>
              Contrôle de qualité en fin de chaîne
            </Text>
            <Text style={styles.introText}>
              Scannez un paquet pour effectuer un contrôle de qualité en fin de chaîne et indiquer s'il y a des défauts à corriger.
            </Text>
          </View>
          
          <Card style={styles.actionCard}>
            <View style={styles.cardHeader}>
              <CheckCircle size={24} color="#8b5cf6" />
              <Text style={styles.cardTitle}>Scanner un paquet</Text>
            </View>
            <Text style={styles.cardDescription}>
              Scannez le code QR d'un paquet pour effectuer un contrôle de qualité
            </Text>
            <Button 
              title="Scanner un paquet" 
              onPress={handleScanPress}
              style={styles.scanButton}
              icon={<ScanLine size={24} color="#ffffff" style={styles.buttonIcon} />}
            />
          </Card>
        </ScrollView>
      </SafeAreaView>
    </RoleBasedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  introSection: {
    marginBottom: 24,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
  },
  introText: {
    fontSize: 16,
    color: '#64748b',
    lineHeight: 24,
  },
  actionCard: {
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 12,
  },
  cardDescription: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 16,
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#8b5cf6',
  },
  buttonIcon: {
    marginRight: 8,
  },
});
