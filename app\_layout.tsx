import { Slot, Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useFonts } from '@expo-google-fonts/inter';
import React, { useEffect } from 'react';
import { AuthProvider } from '@/context/AuthContext';
import { loadSounds, unloadSounds } from '@/utils/sound';

export default function RootLayout() {
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Load sounds when the app starts and unload them when it closes
  useEffect(() => {
    // Load sounds
    loadSounds();

    // Cleanup function to unload sounds when component unmounts
    return () => {
      unloadSounds();
    };
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
     <AuthProvider>


        <StatusBar style="auto" />
        <Slot/>
        </AuthProvider>
    </GestureHandlerRootView>
  );
}