import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { Clock, Package, TrendingUp } from 'lucide-react-native';
import Card from '@/components/ui/Card';

interface HourlyData {
  hour: string;
  count: number;
  scans: Array<{
    time: string;
    packetId: string;
    user: string;
  }>;
}

interface OrderTimelineProps {
  hourlyActivity: {
    debutGM: HourlyData[];
    finGM: HourlyData[];
    summary: {
      totalHoursActive: number;
      peakHourDebutGM: {
        hour: string;
        count: number;
      } | null;
      peakHourFinGM: {
        hour: string;
        count: number;
      } | null;
    };
  };
  packets: {
    totals: {
      enCours: number;
      enAttente: number;
      done: number;
      retouche: number;
      finnishing: number;
      total: number;
    };
    scannedCount: {
      debutGM: number;
      finGM: number;
    };
    productivity: {
      completionRate: number;
      averageTimePerPacket: number | null;
    };
  };
  timeline: {
    orderStartTime: string | null;
    orderFinMontage: string | null;
    orderFinished: string | null;
    montagePhase: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
    finitionPhase: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
    totalDuration: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
  };
}

export default function OrderTimeline({ hourlyActivity, packets, timeline }: OrderTimelineProps) {
  // Generate hours from 7:00 to 17:00
  const generateHours = () => {
    const hours = [];
    for (let i = 7; i <= 17; i++) {
      hours.push(`${i.toString().padStart(2, '0')}:00`);
    }
    return hours;
  };

  const hours = generateHours();

  // Create a map for quick lookup of hourly data
  const debutGMMap = new Map(hourlyActivity.debutGM.map(item => [item.hour.split(' ')[1], item.count]));
  const finGMMap = new Map(hourlyActivity.finGM.map(item => [item.hour.split(' ')[1], item.count]));

  // Find max count for scaling
  const maxCount = Math.max(
    ...hourlyActivity.debutGM.map(item => item.count),
    ...hourlyActivity.finGM.map(item => item.count),
    1
  );

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}min` : `${mins}min`;
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <ScrollView style={styles.container}>
      {/* Statistics Cards */}
      <View style={styles.statsGrid}>
        <Card style={styles.statCard}>
          <View style={styles.statHeader}>
            <Package size={20} color="#3b82f6" />
            <Text style={styles.statTitle}>Paquets Totaux</Text>
          </View>
          <Text style={styles.statValue}>{packets.totals.total}</Text>
          <Text style={styles.statSubtext}>
            {packets.totals.done} terminés • {packets.totals.enCours} en cours
          </Text>
        </Card>

        <Card style={styles.statCard}>
          <View style={styles.statHeader}>
            <TrendingUp size={20} color="#10b981" />
            <Text style={styles.statTitle}>Taux de Completion</Text>
          </View>
          <Text style={styles.statValue}>{packets.productivity.completionRate}%</Text>
          <Text style={styles.statSubtext}>
            {packets.scannedCount.finGM} / {packets.scannedCount.debutGM} scannés
          </Text>
        </Card>

        <Card style={styles.statCard}>
          <View style={styles.statHeader}>
            <Clock size={20} color="#f59e0b" />
            <Text style={styles.statTitle}>Temps Moyen</Text>
          </View>
          <Text style={styles.statValue}>
            {packets.productivity.averageTimePerPacket 
              ? formatDuration(packets.productivity.averageTimePerPacket)
              : 'N/A'
            }
          </Text>
          <Text style={styles.statSubtext}>par paquet</Text>
        </Card>
      </View>

      {/* Timeline */}
      <Card style={styles.timelineCard}>
        <Text style={styles.timelineTitle}>Activité Horaire (7h - 17h)</Text>
        
        <View style={styles.timeline}>
          <View style={styles.timelineHeader}>
            <Text style={styles.timelineLabel}>Heure</Text>
            <Text style={styles.timelineLabel}>Début GM</Text>
            <Text style={styles.timelineLabel}>Fin GM</Text>
            <Text style={styles.timelineLabel}>Terminés</Text>
          </View>

          {hours.map((hour) => {
            const debutCount = debutGMMap.get(hour) || 0;
            const finCount = finGMMap.get(hour) || 0;
            const completedPackets = Math.min(debutCount, finCount);

            return (
              <View key={hour} style={styles.timelineRow}>
                <Text style={styles.hourText}>{hour}</Text>
                
                {/* Début GM Bar */}
                <View style={styles.barContainer}>
                  <View 
                    style={[
                      styles.bar, 
                      styles.debutBar,
                      { width: `${(debutCount / maxCount) * 100}%` }
                    ]} 
                  />
                  <Text style={styles.countText}>{debutCount}</Text>
                </View>

                {/* Fin GM Bar */}
                <View style={styles.barContainer}>
                  <View 
                    style={[
                      styles.bar, 
                      styles.finBar,
                      { width: `${(finCount / maxCount) * 100}%` }
                    ]} 
                  />
                  <Text style={styles.countText}>{finCount}</Text>
                </View>

                {/* Completed Packets */}
                <View style={styles.completedContainer}>
                  <Text style={[
                    styles.completedText,
                    completedPackets > 0 && styles.completedActive
                  ]}>
                    {completedPackets}
                  </Text>
                </View>
              </View>
            );
          })}
        </View>

        {/* Legend */}
        <View style={styles.legend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, styles.debutBar]} />
            <Text style={styles.legendText}>Début GM</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, styles.finBar]} />
            <Text style={styles.legendText}>Fin GM</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: '#10b981' }]} />
            <Text style={styles.legendText}>Paquets Terminés</Text>
          </View>
        </View>
      </Card>

      {/* Phase Information */}
      {timeline.montagePhase && (
        <Card style={styles.phaseCard}>
          <Text style={styles.phaseTitle}>Phase de Montage</Text>
          <Text style={styles.phaseTime}>
            {formatTime(timeline.montagePhase.start)} - {formatTime(timeline.montagePhase.end)}
          </Text>
          <Text style={styles.phaseDuration}>
            Durée: {formatDuration(timeline.montagePhase.durationMinutes)}
          </Text>
        </Card>
      )}

      {timeline.finitionPhase && (
        <Card style={styles.phaseCard}>
          <Text style={styles.phaseTitle}>Phase de Finition</Text>
          <Text style={styles.phaseTime}>
            {formatTime(timeline.finitionPhase.start)} - {formatTime(timeline.finitionPhase.end)}
          </Text>
          <Text style={styles.phaseDuration}>
            Durée: {formatDuration(timeline.finitionPhase.durationMinutes)}
          </Text>
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    gap: 8,
  },
  statCard: {
    flex: 1,
    minWidth: 100,
    padding: 12,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    fontSize: 12,
    color: '#64748b',
    marginLeft: 4,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 4,
  },
  statSubtext: {
    fontSize: 10,
    color: '#64748b',
  },
  timelineCard: {
    marginBottom: 16,
    padding: 16,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
  },
  timeline: {
    marginBottom: 16,
  },
  timelineHeader: {
    flexDirection: 'row',
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    marginBottom: 8,
  },
  timelineLabel: {
    flex: 1,
    fontSize: 12,
    fontWeight: '600',
    color: '#64748b',
    textAlign: 'center',
  },
  timelineRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    minHeight: 32,
  },
  hourText: {
    flex: 1,
    fontSize: 12,
    color: '#1e293b',
    textAlign: 'center',
    fontWeight: '500',
  },
  barContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 4,
  },
  bar: {
    height: 16,
    borderRadius: 8,
    minWidth: 2,
  },
  debutBar: {
    backgroundColor: '#3b82f6',
  },
  finBar: {
    backgroundColor: '#8b5cf6',
  },
  countText: {
    fontSize: 10,
    color: '#64748b',
    marginLeft: 4,
    minWidth: 16,
  },
  completedContainer: {
    flex: 1,
    alignItems: 'center',
  },
  completedText: {
    fontSize: 12,
    color: '#64748b',
    fontWeight: '500',
  },
  completedActive: {
    color: '#10b981',
    fontWeight: '700',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendText: {
    fontSize: 10,
    color: '#64748b',
  },
  phaseCard: {
    marginBottom: 12,
    padding: 12,
  },
  phaseTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  phaseTime: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 2,
  },
  phaseDuration: {
    fontSize: 12,
    color: '#10b981',
    fontWeight: '500',
  },
});
