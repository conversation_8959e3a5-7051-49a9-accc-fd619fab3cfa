import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Clock, Package, TrendingUp, ChevronLeft, ChevronRight, Calendar } from 'lucide-react-native';
import Card from '@/components/ui/Card';

interface HourlyData {
  hour: string;
  count: number;
  scans: Array<{
    time: string;
    packetId: string;
    user: string;
  }>;
}

interface DailyData {
  date: string;
  totalCount: number;
  hours: HourlyData[];
  scans: Array<{
    time: string;
    packetId: string;
    user: string;
  }>;
}

interface OrderTimelineProps {
  dailyActivity: {
    debutGM: DailyData[];
    finGM: DailyData[];
    summary: {
      totalDaysActive: number;
      peakDayDebutGM: {
        date: string;
        totalCount: number;
      } | null;
      peakDayFinGM: {
        date: string;
        totalCount: number;
      } | null;
      totalDebutGMDays: number;
      totalFinGMDays: number;
    };
  };
  packets: {
    totals: {
      enCours: number;
      enAttente: number;
      done: number;
      retouche: number;
      finnishing: number;
      total: number;
    };
    scannedCount: {
      debutGM: number;
      finGM: number;
    };
    productivity: {
      completionRate: number;
      averageTimePerPacket: number | null;
    };
  };
  timeline: {
    orderStartTime: string | null;
    orderFinMontage: string | null;
    orderFinished: string | null;
    montagePhase: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
    finitionPhase: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
    totalDuration: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
  };
}

export default function OrderTimeline({ dailyActivity, packets, timeline }: OrderTimelineProps) {
  // Get all available dates and set today as default
  const getAllDates = () => {
    const allDates = new Set<string>();
    dailyActivity.debutGM.forEach(day => allDates.add(day.date));
    dailyActivity.finGM.forEach(day => allDates.add(day.date));
    return Array.from(allDates).sort();
  };

  const availableDates = getAllDates();
  // Get today's date in local timezone, formatted as YYYY-MM-DD
  const today = new Date().getFullYear() + '-' +
    String(new Date().getMonth() + 1).padStart(2, '0') + '-' +
    String(new Date().getDate()).padStart(2, '0');
  const defaultDate = availableDates.includes(today) ? today : availableDates[availableDates.length - 1] || today;

  const [selectedDate, setSelectedDate] = useState(defaultDate);

  // Generate hour intervals from 7:00 to 17:00
  const generateHourIntervals = () => {
    const intervals = [];
    for (let i = 7; i <= 16; i++) {
      const startHour = `${i.toString().padStart(2, '0')}:00`;
      const endHour = `${(i + 1).toString().padStart(2, '0')}:00`;
      intervals.push({
        display: `${startHour} → ${endHour}`,
        key: startHour // Keep the original hour format for data lookup
      });
    }
    return intervals;
  };

  const hourIntervals = generateHourIntervals();

  // Get data for selected date
  const selectedDebutGMDay = dailyActivity.debutGM.find(day => day.date === selectedDate);
  const selectedFinGMDay = dailyActivity.finGM.find(day => day.date === selectedDate);

  // Create maps for quick lookup of hourly data for selected date
  const debutGMMap = new Map();
  const finGMMap = new Map();

  // Helper function to convert UTC hour string to local time
  const convertUTCHourToLocal = (hourString: string) => {
    // hourString format: "2024-01-15 09:00"
    const utcDate = new Date(hourString + ':00.000Z'); // Add seconds and Z to make it explicit UTC
    const localHour = utcDate.getHours();
    return `${localHour.toString().padStart(2, '0')}:00`;
  };

  if (selectedDebutGMDay) {
    selectedDebutGMDay.hours.forEach(hourData => {
      const localHour = convertUTCHourToLocal(hourData.hour);
      debutGMMap.set(localHour, hourData.count);
    });
  }

  if (selectedFinGMDay) {
    selectedFinGMDay.hours.forEach(hourData => {
      const localHour = convertUTCHourToLocal(hourData.hour);
      finGMMap.set(localHour, hourData.count);
    });
  }

  // Find max count for scaling
  const maxCount = Math.max(
    ...(selectedDebutGMDay?.hours.map(item => item.count) || [0]),
    ...(selectedFinGMDay?.hours.map(item => item.count) || [0]),
    1
  );

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}min` : `${mins}min`;
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatDate = (dateString: string) => {
    // If dateString is just a date (YYYY-MM-DD), treat it as local date
    if (dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
      const [year, month, day] = dateString.split('-');
      const localDate = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
      return localDate.toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    }
    // For full datetime strings, use as is (they should already be in correct timezone)
    return new Date(dateString).toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const currentIndex = availableDates.indexOf(selectedDate);
    if (direction === 'prev' && currentIndex > 0) {
      setSelectedDate(availableDates[currentIndex - 1]);
    } else if (direction === 'next' && currentIndex < availableDates.length - 1) {
      setSelectedDate(availableDates[currentIndex + 1]);
    }
  };

  // Calculate packets that are "en cours" (have debutGM but no finGM)
  const calculateEnCoursPackets = () => {
    const allDebutScans: Array<{time: string, packetId: string}> = [];
    const allFinScans: Array<{time: string, packetId: string}> = [];

    // Collect all scans from all days
    dailyActivity.debutGM.forEach(day => {
      day.scans.forEach(scan => {
        allDebutScans.push({time: scan.time, packetId: scan.packetId});
      });
    });

    dailyActivity.finGM.forEach(day => {
      day.scans.forEach(scan => {
        allFinScans.push({time: scan.time, packetId: scan.packetId});
      });
    });

    // Create set of packets that have finGM
    const finishedPacketIds = new Set(allFinScans.map(scan => scan.packetId));

    // Count packets that have debutGM but no finGM
    const enCoursPacketIds = new Set();
    allDebutScans.forEach(scan => {
      if (!finishedPacketIds.has(scan.packetId)) {
        enCoursPacketIds.add(scan.packetId);
      }
    });

    return enCoursPacketIds.size;
  };

  // Calculate accurate packets per hour based on actual time spent
  const calculatePacketsPerHour = () => {
    const allDebutScans: Array<{time: string, packetId: string}> = [];
    const allFinScans: Array<{time: string, packetId: string}> = [];

    // Collect all scans from all days
    dailyActivity.debutGM.forEach(day => {
      day.scans.forEach(scan => {
        allDebutScans.push({time: scan.time, packetId: scan.packetId});
      });
    });

    dailyActivity.finGM.forEach(day => {
      day.scans.forEach(scan => {
        allFinScans.push({time: scan.time, packetId: scan.packetId});
      });
    });

    // Create maps for quick lookup
    const debutScanMap = new Map(allDebutScans.map(scan => [scan.packetId, scan.time]));
    const finScanMap = new Map(allFinScans.map(scan => [scan.packetId, scan.time]));

    let totalPacketHours = 0; // Total hours spent on all packets
    let completedPackets = 0;

    // Calculate time spent for each completed packet
    debutScanMap.forEach((debutTime, packetId) => {
      const finTime = finScanMap.get(packetId);
      if (finTime) {
        // Calculate duration in hours
        const debutUTC = new Date(debutTime + (debutTime.includes('Z') ? '' : 'Z'));
        const finUTC = new Date(finTime + (finTime.includes('Z') ? '' : 'Z'));
        const durationHours = (finUTC.getTime() - debutUTC.getTime()) / (1000 * 60 * 60);

        totalPacketHours += durationHours;
        completedPackets += 1;
      }
    });

    if (totalPacketHours === 0) {
      return 'N/A';
    }

    // Packets per hour = total completed packets / total hours spent
    const packetsPerHour = completedPackets / totalPacketHours;
    return packetsPerHour.toFixed(1);
  };

  return (
    <ScrollView style={styles.container}>
      {/* Statistics Cards */}
      <View style={styles.statsGrid}>
        <Card style={styles.statCard}>
          <View style={styles.statHeader}>
            <Package size={20} color="#3b82f6" />
            <Text style={styles.statTitle}>Paquets Totaux</Text>
          </View>
          <Text style={styles.statValue}>{packets.totals.total}</Text>
          <Text style={styles.statSubtext}>
            {packets.scannedCount.finGM} fin GM • {calculateEnCoursPackets()} en cours
          </Text>
        </Card>

        <Card style={styles.statCard}>
          <View style={styles.statHeader}>
            <TrendingUp size={20} color="#10b981" />
            <Text style={styles.statTitle}>Taux de Completion</Text>
          </View>
          <Text style={styles.statValue}>{packets.productivity.completionRate}%</Text>
          <Text style={styles.statSubtext}>
            {packets.scannedCount.finGM} / {packets.scannedCount.debutGM} scannés
          </Text>
        </Card>

        <Card style={styles.statCard}>
          <View style={styles.statHeader}>
            <Clock size={20} color="#f59e0b" />
            <Text style={styles.statTitle}>Nbr Packet par Heure</Text>
          </View>
          <Text style={styles.statValue}>
            {calculatePacketsPerHour()}
          </Text>
          <Text style={styles.statSubtext}>packets/heure</Text>
        </Card>
      </View>

      {/* Date Navigation */}
      <Card style={styles.dateNavigationCard}>
        <View style={styles.dateNavigation}>
          <TouchableOpacity
            style={[styles.navButton, availableDates.indexOf(selectedDate) === 0 && styles.navButtonDisabled]}
            onPress={() => navigateDate('prev')}
            disabled={availableDates.indexOf(selectedDate) === 0}
          >
            <ChevronLeft size={20} color={availableDates.indexOf(selectedDate) === 0 ? "#94a3b8" : "#1e293b"} />
          </TouchableOpacity>

          <View style={styles.dateDisplay}>
            <Calendar size={16} color="#64748b" />
            <Text style={styles.dateText}>{formatDate(selectedDate)}</Text>
          </View>

          <TouchableOpacity
            style={[styles.navButton, availableDates.indexOf(selectedDate) === availableDates.length - 1 && styles.navButtonDisabled]}
            onPress={() => navigateDate('next')}
            disabled={availableDates.indexOf(selectedDate) === availableDates.length - 1}
          >
            <ChevronRight size={20} color={availableDates.indexOf(selectedDate) === availableDates.length - 1 ? "#94a3b8" : "#1e293b"} />
          </TouchableOpacity>
        </View>
      </Card>

      {/* Timeline */}
      <Card style={styles.timelineCard}>
        <Text style={styles.timelineTitle}>Activité Horaire (7h00 → 17h00)</Text>

        <View style={styles.timeline}>
          <View style={styles.timelineHeader}>
            <Text style={styles.timelineLabel}>Intervalle</Text>
            <Text style={styles.timelineLabel}>Packet Entrée</Text>
            <Text style={styles.timelineLabel}>Packet Sortie</Text>
          </View>

          {hourIntervals.map((interval) => {
            const debutCount = debutGMMap.get(interval.key) || 0;
            const finCount = finGMMap.get(interval.key) || 0;

            return (
              <View key={interval.key} style={styles.timelineRow}>
                <Text style={styles.hourText}>{interval.display}</Text>

                {/* Packet Entrée (Début GM) */}
                <View style={styles.countContainer}>
                  <View style={[styles.countBadge, styles.entreeeBadge]}>
                    <Text style={styles.badgeText}>{debutCount}</Text>
                  </View>
                </View>

                {/* Packet Sortie (Fin GM) */}
                <View style={styles.countContainer}>
                  <View style={[styles.countBadge, styles.sortieBadge]}>
                    <Text style={styles.badgeText}>{finCount}</Text>
                  </View>
                </View>
              </View>
            );
          })}
        </View>

        {/* Legend */}
        <View style={styles.legend}>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: '#3b82f6' }]} />
            <Text style={styles.legendText}>Packet Entrée (Début GM)</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendColor, { backgroundColor: '#10b981' }]} />
            <Text style={styles.legendText}>Packet Sortie (Fin GM)</Text>
          </View>
        </View>
      </Card>

      {/* Phase Information */}
      {timeline.montagePhase && (
        <Card style={styles.phaseCard}>
          <Text style={styles.phaseTitle}>Phase de Montage</Text>
          <Text style={styles.phaseTime}>
            {formatTime(timeline.montagePhase.start)} - {formatTime(timeline.montagePhase.end)}
          </Text>
          <Text style={styles.phaseDuration}>
            Durée: {formatDuration(timeline.montagePhase.durationMinutes)}
          </Text>
        </Card>
      )}

      {timeline.finitionPhase && (
        <Card style={styles.phaseCard}>
          <Text style={styles.phaseTitle}>Phase de Finition</Text>
          <Text style={styles.phaseTime}>
            {formatTime(timeline.finitionPhase.start)} - {formatTime(timeline.finitionPhase.end)}
          </Text>
          <Text style={styles.phaseDuration}>
            Durée: {formatDuration(timeline.finitionPhase.durationMinutes)}
          </Text>
        </Card>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
    gap: 8,
  },
  statCard: {
    flex: 1,
    minWidth: 100,
    padding: 12,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statTitle: {
    fontSize: 12,
    color: '#64748b',
    marginLeft: 4,
    fontWeight: '500',
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 4,
  },
  statSubtext: {
    fontSize: 10,
    color: '#64748b',
  },
  timelineCard: {
    marginBottom: 16,
    padding: 16,
  },
  timelineTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 16,
  },
  timeline: {
    marginBottom: 16,
  },
  timelineHeader: {
    flexDirection: 'row',
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    marginBottom: 8,
  },
  timelineLabel: {
    flex: 1,
    fontSize: 12,
    fontWeight: '600',
    color: '#64748b',
    textAlign: 'center',
  },
  timelineRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 4,
    minHeight: 32,
  },
  dateNavigationCard: {
    marginBottom: 16,
    padding: 16,
  },
  dateNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  navButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#f1f5f9',
  },
  navButtonDisabled: {
    backgroundColor: '#f8fafc',
  },
  dateDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 16,
  },
  dateText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 8,
    textAlign: 'center',
  },
  hourText: {
    flex: 1.2,
    fontSize: 11,
    color: '#1e293b',
    textAlign: 'center',
    fontWeight: '500',
  },
  countContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  countBadge: {
    minWidth: 32,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  entreeeBadge: {
    backgroundColor: '#3b82f6',
  },
  sortieBadge: {
    backgroundColor: '#10b981',
  },
  badgeText: {
    fontSize: 12,
    color: '#ffffff',
    fontWeight: '600',
  },
  legend: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 4,
  },
  legendText: {
    fontSize: 10,
    color: '#64748b',
  },
  phaseCard: {
    marginBottom: 12,
    padding: 12,
  },
  phaseTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 4,
  },
  phaseTime: {
    fontSize: 12,
    color: '#64748b',
    marginBottom: 2,
  },
  phaseDuration: {
    fontSize: 12,
    color: '#10b981',
    fontWeight: '500',
  },
});
