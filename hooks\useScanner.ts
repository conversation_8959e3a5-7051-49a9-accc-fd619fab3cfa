import { useState, useCallback } from 'react';
import { ScanResult, ScanType } from '@/types';
import { sendScanResult } from '@/utils/api';

export default function useScanner() {
  const [isScanning, setIsScanning] = useState(false);
  const [scanHistory, setScanHistory] = useState<ScanResult[]>([]);
  const [currentResult, setCurrentResult] = useState<ScanResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const startScanning = useCallback(() => {
    setIsScanning(true);
    setError(null);
    setCurrentResult(null);
  }, []);

  const stopScanning = useCallback(() => {
    setIsScanning(false);
  }, []);

  const processBarcode = useCallback(async (barcode: string, type: ScanType) => {
    setIsScanning(false);

    try {
      const response = await sendScanResult(type, barcode);
      
      const newResult: ScanResult = {
        id: Date.now().toString(),
        type,
        barcode,
        timestamp: Date.now(),
        success: response.success,
        message: response.error || 'Scan successful',
      };
      
      setCurrentResult(newResult);
      
      // Add to history
      setScanHistory(prev => [newResult, ...prev]);
      
      return response.success;
    } catch (error) {
      console.error('Error processing barcode:', error);
      setError('Failed to process scan. Please try again.');
      
      const failedResult: ScanResult = {
        id: Date.now().toString(),
        type,
        barcode,
        timestamp: Date.now(),
        success: false,
        message: error instanceof Error ? error.message : 'Unknown error',
      };
      
      setCurrentResult(failedResult);
      setScanHistory(prev => [failedResult, ...prev]);
      
      return false;
    }
  }, []);

  return {
    isScanning,
    scanHistory,
    currentResult,
    error,
    startScanning,
    stopScanning,
    processBarcode,
  };
}