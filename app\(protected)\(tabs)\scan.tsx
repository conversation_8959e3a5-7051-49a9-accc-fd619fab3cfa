import React from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useAuth } from '@/context/AuthContext';
import ScanOptions from '@/components/scanner/ScanOptions';

export default function ScanScreen() {
  const { user } = useAuth();
  const userRole = user?.role || 'admin';

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {userRole === 'ouvrier_machine'
            ? 'Scanner Paquet'
            : userRole === 'responsable_chaine'
            ? 'Options de Scan'
            : 'Barcode Scanner'}
        </Text>
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.introSection}>
          <Text style={styles.introTitle}>
            {userRole === 'ouvrier_machine'
              ? 'Scanner un paquet'
              : userRole === 'responsable_chaine'
              ? 'Choisissez une option de scan'
              : 'Select a scan type to begin'}
          </Text>
          <Text style={styles.introText}>
            {userRole === 'ouvrier_machine'
              ? 'Scannez le code QR d\'un paquet pour enregistrer son traitement'
              : userRole === 'responsable_chaine'
              ? 'Sélectionnez le type de scan approprié pour vos opérations'
              : 'Choose the appropriate scan type based on what you need to scan in your warehouse or production floor'}
          </Text>
        </View>

        <ScanOptions />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
  },
  content: {
    flex: 1,
  },
  introSection: {
    padding: 16,
    paddingBottom: 8,
  },
  introTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 8,
  },
  introText: {
    fontSize: 14,
    color: '#64748b',
    lineHeight: 20,
  },
});