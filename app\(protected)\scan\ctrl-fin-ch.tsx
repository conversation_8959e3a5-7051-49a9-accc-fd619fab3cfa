import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
  Switch
} from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, CircleCheck, CircleAlert, CheckCircle, X } from 'lucide-react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';

import { useAuth } from '@/context/AuthContext';
import { apiRequest } from '@/utils/api';
import { playBeepSound } from '@/utils/sound';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import RoleBasedRoute from '@/components/auth/RoleBasedRoute';

interface ScanResponse {
  packet: {
    qrCode: string;
    status: string;
    pieces: Array<{
      _id: string;
      reference: string;
      status?: string;
      defaut?: string;
    }>;
    scans: Array<{
      type: string;
      time: string;
      user?: string;
    }>;
  };
  order: {
    orderNumber: string;
    status: string;
  };
}

export default function ControlFinChaineScreen() {
  const { user } = useAuth();
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    qrCode?: string;
    pieces?: Array<{
      _id: string;
      reference: string;
      hasDefaut: boolean;
      defaut?: string;
    }>;
  } | null>(null);

  // State for defaut pieces - now storing arrays of defects for each piece
  const [defautPieces, setDefautPieces] = useState<Record<string, string[]>>({});
  const [hasDefauts, setHasDefauts] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const ITEMS_PER_PAGE = 10;

  // Define handleGoBack before using it in useEffect
  const handleGoBack = React.useCallback(() => {
    router.back();
  }, []);

  // Add a useEffect hook for auto-redirect when packet is already in finishing status
  React.useEffect(() => {
    if (scanned && result && !result.success && result.message === 'Le packet est déjà en finition.') {
      const timer = setTimeout(() => {
        handleGoBack();
      }, 3000); // Redirect after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [scanned, result, handleGoBack]);

  if (!permission) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Nous avons besoin de votre permission pour utiliser la caméra
        </Text>
        <Button
          title="Autoriser la caméra"
          onPress={requestPermission}
          style={styles.permissionButton}
        />
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.cancelButton}
        >
          <Text style={styles.cancelText}>Annuler</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleBarCodeScanned = async ({ data }: { data: string }) => {
    if (scanned || isProcessing) return;

    setScanned(true);
    setIsProcessing(true);

    try {
      console.log('Scanning barcode:', data);

      // Get the packet info using the get-by-scan endpoint (doesn't mark the packet yet)
      const response = await apiRequest<ScanResponse>('/packets/get-by-scan', {
        method: 'POST',
        body: {
          barcode: data,
        },
      });

      console.log('Scan response:', response);

      if (response.success && response.data && response.data.packet) {
        // Check if the packet is already in finishing status
        if (response.data.packet.status === 'finnishing') {
          // If packet is already in finishing status, show error message and redirect back
          setResult({
            success: false,
            message: 'Le packet est déjà en finition.',
          });
          return;
        }

        // Check if the packet has a finGM scan
        const hasFinGMScan = response.data.packet.scans &&
                            response.data.packet.scans.some(scan => scan.type === 'finGM');

        if (!hasFinGMScan) {
          // If no finGM scan, show error message
          setResult({
            success: false,
            message: 'Packet n\'est pas terminé toutes ses opérations. Veuillez terminer toutes les opérations avant le contrôle fin chaîne.',
          });
          return;
        }

        // Packet has finGM scan, proceed with normal flow
        // Trigger success haptic feedback
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Play beep sound
        playBeepSound();

        // Initialize all pieces with no defauts, regardless of their previous state
        const pieces = response.data.packet.pieces.map(piece => ({
          ...piece,
          hasDefaut: false, // Always set to false initially
          defaut: '', // Clear any previous defaut description
        }));

        setResult({
          success: true,
          message: 'Paquet scanné avec succès. Veuillez indiquer s\'il y a des défauts.',
          qrCode: response.data.packet.qrCode,
          pieces,
        });

        // Always start with no defauts selected
        setHasDefauts(false);

        // Initialize empty defautPieces state
        setDefautPieces({});
      } else {
        // Handle specific error messages from the server
        let errorMessage = response.error || 'Échec du scan';

        // Check for specific error messages
        if (errorMessage.includes('Packet not found') || errorMessage.includes('404')) {
          errorMessage = 'Paquet non trouvé. Veuillez vérifier le code-barres.';
        } else if (errorMessage.includes('Invalid barcode format')) {
          errorMessage = 'Format de code-barres invalide. Format attendu: numeroOF/numeroPaquet';
        } else if (errorMessage.includes('Order not found')) {
          errorMessage = 'Ordre de fabrication non trouvé pour ce paquet.';
        }

        setResult({
          success: false,
          message: errorMessage,
        });

        console.log('Scan error:', response.error);
      }
    } catch (error) {
      console.error('Error scanning packet for ctrl-fin-ch:', error);
      setResult({
        success: false,
        message: 'Une erreur est survenue lors du scan. Veuillez réessayer.',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleToggleDefaut = (pieceId: string, hasDefaut: boolean) => {
    if (result && result.pieces) {
      const updatedPieces = result.pieces.map(piece => {
        if (piece._id === pieceId) {
          return { ...piece, hasDefaut };
        }
        return piece;
      });

      setResult({ ...result, pieces: updatedPieces });

      // Update defautPieces state
      const newDefautPieces = { ...defautPieces };
      if (!hasDefaut) {
        delete newDefautPieces[pieceId];
      } else if (!newDefautPieces[pieceId]) {
        // Initialize with an array containing one empty string
        newDefautPieces[pieceId] = [''];
      }

      setDefautPieces(newDefautPieces);
      setHasDefauts(Object.keys(newDefautPieces).length > 0);
    }
  };

  const handleDefautChange = (pieceId: string, index: number, defaut: string) => {
    const defauts = [...(defautPieces[pieceId] || [])];
    defauts[index] = defaut;
    setDefautPieces({ ...defautPieces, [pieceId]: defauts });
  };

  const handleAddDefaut = (pieceId: string) => {
    const defauts = [...(defautPieces[pieceId] || []), ''];
    setDefautPieces({ ...defautPieces, [pieceId]: defauts });
  };

  const handleRemoveDefaut = (pieceId: string, index: number) => {
    const defauts = [...(defautPieces[pieceId] || [])];
    if (defauts.length <= 1) {
      // If this is the last defaut, don't remove it, just clear it
      setDefautPieces({ ...defautPieces, [pieceId]: [''] });
      return;
    }

    // Remove the defaut at the specified index
    defauts.splice(index, 1);
    setDefautPieces({ ...defautPieces, [pieceId]: defauts });
  };

  const handleSubmit = async () => {
    if (!result || !result.qrCode) return;

    setIsProcessing(true);

    try {
      // Format defautPieces for the API
      let defautPiecesArray = null;

      if (hasDefauts) {
        // Only include pieces that have defauts
        // Format the defautPieces as a simple array of strings
        // where each string is in the format "pieceId:defautDescription"
        defautPiecesArray = [];

        // Process each piece with defauts
        Object.entries(defautPieces).forEach(([pieceId, defauts]) => {
          if (Array.isArray(defauts)) {
            // Filter out empty defaut descriptions
            const validDefauts = defauts.filter(defaut => defaut.trim() !== '');

            // Add each defaut as a separate entry in the format "pieceId:defautDescription"
            validDefauts.forEach(defaut => {
              defautPiecesArray.push(`${pieceId}:${defaut}`);
            });
          }
        });

        // If no defauts were actually specified, show an error
        if (defautPiecesArray.length === 0) {
          setResult({
            success: false,
            message: 'Veuillez spécifier au moins un défaut ou sélectionner "Pas de défaut"',
          });
          setIsProcessing(false);
          return;
        }
      }

      console.log('Sending request to API:', {
        barcode: result.qrCode,
        defautPieces: defautPiecesArray,
        user: user?.email,
      });

      // Now we call the actual scan-ctrl-fin-ch endpoint to mark the packet
      const response = await apiRequest('/packets/scan-ctrl-fin-ch', {
        method: 'POST',
        body: {
          barcode: result.qrCode,
          defautPieces: defautPiecesArray,
          user: user?.email, // Include user email for tracking
        },
      });

      if (response.success) {
        // Trigger success haptic feedback
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Play beep sound
        playBeepSound();

        setResult({
          success: true,
          message: hasDefauts
            ? 'Paquet marqué pour retouche avec pièces défectueuses'
            : 'Le paquet est prêt à être fini',
          qrCode: result.qrCode,
        });

        // Auto-refresh after 2 seconds to allow for new scan
        setTimeout(() => {
          handleNewScan();
        }, 2000);
      } else {
        setResult({
          success: false,
          message: response.error || 'Échec de l\'enregistrement',
        });
      }
    } catch (error) {
      console.error('Error submitting control fin chaine:', error);
      setResult({
        success: false,
        message: 'Une erreur est survenue lors de l\'enregistrement',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNewScan = () => {
    setScanned(false);
    setResult(null);
    setDefautPieces({});
    setHasDefauts(false);
    setCurrentPage(1);
  };

  // Pagination handlers
  const handleNextPage = () => {
    if (result?.pieces && currentPage < Math.ceil(result.pieces.length / ITEMS_PER_PAGE)) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  // Get current page items
  const getCurrentPageItems = () => {
    if (!result?.pieces) return [];

    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return result.pieces.slice(startIndex, endIndex);
  };

  // Render the result screen with defaut selection
  if (scanned && result && result.success && result.pieces) {
    return (
      <RoleBasedRoute allowedRoles={['admin', 'controlleur_fin_chaine']}>
        <View style={styles.resultContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={handleGoBack}
              style={styles.backButton}
            >
              <ArrowLeft color="#ffffff" size={24} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Contrôle Fin de Chaîne</Text>
            <View style={styles.placeholder} />
          </View>

          <ScrollView style={styles.content}>
            <Card style={styles.resultCard}>
              <View style={styles.resultHeader}>
                <CircleCheck size={32} color="#10b981" />
                <Text style={styles.resultTitle}>Paquet Scanné</Text>
              </View>

              <Text style={styles.qrCode}>{result.qrCode}</Text>

              <View style={styles.defautSection}>
                <Text style={styles.sectionTitle}>État du paquet</Text>

                <View style={styles.defautButtonsContainer}>
                  <Button
                    title="Pas de défaut"
                    onPress={() => setHasDefauts(false)}
                    style={{
                      ...styles.defautButton,
                      ...(!hasDefauts ? styles.activeDefautButton : styles.inactiveButton)
                    }}
                    variant="primary"
                    icon={!hasDefauts ? <CheckCircle size={20} color="#ffffff" style={styles.buttonIcon} /> : undefined}
                  />
                  <Button
                    title="Signaler des défauts"
                    onPress={() => setHasDefauts(true)}
                    style={{
                      ...styles.defautButton,
                      ...(hasDefauts ? styles.activeDefectButton : styles.inactiveButton)
                    }}
                    variant="primary"
                    icon={hasDefauts ? <CircleAlert size={20} color="#ffffff" style={styles.buttonIcon} /> : undefined}
                  />
                </View>

                {hasDefauts && (
                  <View style={styles.piecesList}>
                    <Text style={styles.piecesTitle}>
                      Sélectionnez les pièces défectueuses:
                      {result.pieces && result.pieces.length > ITEMS_PER_PAGE && (
                        <Text style={styles.paginationInfo}>
                          {' '}(Page {currentPage}/{Math.ceil(result.pieces.length / ITEMS_PER_PAGE)})
                        </Text>
                      )}
                    </Text>

                    {getCurrentPageItems().map((piece, index) => {
                      // Calculate the actual piece index in the full list
                      const actualIndex = (currentPage - 1) * ITEMS_PER_PAGE + index;

                      return (
                        <View key={piece._id} style={{
                          ...styles.pieceItem,
                          ...(piece.hasDefaut ? styles.pieceItemSelected : {})
                        }}>
                          <TouchableOpacity
                            style={styles.pieceHeader}
                            onPress={() => handleToggleDefaut(piece._id, !piece.hasDefaut)}
                          >
                            <View style={styles.pieceInfo}>
                              <Text style={styles.pieceNumber}>
                                Pièce {actualIndex + 1}
                              </Text>
                              <Text style={styles.pieceReference}>
                                {piece.reference}
                              </Text>
                            </View>
                            <View style={{
                              ...styles.pieceStatusIndicator,
                              ...(piece.hasDefaut ? styles.pieceStatusDefaut : styles.pieceStatusOk)
                            }}>
                              {piece.hasDefaut ? (
                                <CircleAlert size={20} color="#ffffff" />
                              ) : (
                                <CheckCircle size={20} color="#ffffff" />
                              )}
                            </View>
                          </TouchableOpacity>

                          {piece.hasDefaut && (
                            <View style={styles.defautInputContainer}>
                              <View style={styles.defautHeaderRow}>
                                <Text style={styles.defautInputLabel}>
                                  Description des défauts:
                                </Text>
                                <TouchableOpacity
                                  style={styles.addDefautButton}
                                  onPress={() => handleAddDefaut(piece._id)}
                                >
                                  <Text style={styles.addDefautButtonText}>+ Ajouter un défaut</Text>
                                </TouchableOpacity>
                              </View>

                              {(defautPieces[piece._id] || ['']).map((defaut, index) => (
                                <View key={index} style={styles.defautInputRow}>
                                  <Input
                                    placeholder="Ex: Rayure, cassure, défaut de peinture..."
                                    value={defaut}
                                    onChangeText={(text) => handleDefautChange(piece._id, index, text)}
                                    containerStyle={{ ...styles.defautInput, flex: 1 }}
                                    multiline={true}
                                    numberOfLines={2}
                                  />
                                  {(defautPieces[piece._id]?.length || 0) > 1 && (
                                    <TouchableOpacity
                                      style={styles.removeDefautButton}
                                      onPress={() => handleRemoveDefaut(piece._id, index)}
                                    >
                                      <X size={20} color="#ef4444" />
                                    </TouchableOpacity>
                                  )}
                                </View>
                              ))}
                            </View>
                          )}
                        </View>
                      );
                    })}

                    {/* Pagination controls */}
                    {result.pieces && result.pieces.length > ITEMS_PER_PAGE && (
                      <View style={styles.paginationControls}>
                        <Button
                          title="Précédent"
                          onPress={handlePrevPage}
                          variant="outline"
                          style={{
                            ...styles.paginationButton,
                            ...(currentPage === 1 ? styles.disabledButton : {})
                          }}
                          disabled={currentPage === 1}
                        />
                        <Text style={styles.paginationText}>
                          Page {currentPage} sur {Math.ceil(result.pieces.length / ITEMS_PER_PAGE)}
                        </Text>
                        <Button
                          title="Suivant"
                          onPress={handleNextPage}
                          variant="outline"
                          style={{
                            ...styles.paginationButton,
                            ...(currentPage >= Math.ceil(result.pieces.length / ITEMS_PER_PAGE) ? styles.disabledButton : {})
                          }}
                          disabled={currentPage >= Math.ceil(result.pieces.length / ITEMS_PER_PAGE)}
                        />
                      </View>
                    )}
                  </View>
                )}
              </View>

              <View style={styles.buttonContainer}>
                <Button
                  title={isProcessing ? 'Enregistrement...' : 'Enregistrer le contrôle'}
                  onPress={handleSubmit}
                  isLoading={isProcessing}
                  style={styles.submitButton}
                  disabled={isProcessing}
                />
                <Button
                  title="Nouveau scan"
                  onPress={handleNewScan}
                  variant="outline"
                  style={styles.button}
                  disabled={isProcessing}
                />
                <Button
                  title="Retour"
                  onPress={handleGoBack}
                  variant="secondary"
                  style={styles.button}
                  disabled={isProcessing}
                />
              </View>
            </Card>
          </ScrollView>
        </View>
      </RoleBasedRoute>
    );
  }

  // Render the success screen after submitting
  if (scanned && result && result.success && !result.pieces) {
    return (
      <RoleBasedRoute allowedRoles={['admin', 'controlleur_fin_chaine']}>
        <View style={styles.resultContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={handleGoBack}
              style={styles.backButton}
            >
              <ArrowLeft color="#ffffff" size={24} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Contrôle Fin de Chaîne</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.successContainer}>
            <CircleCheck size={64} color="#10b981" />
            <Text style={styles.successMessage}>{result.message}</Text>
            <Text style={styles.qrCode}>{result.qrCode}</Text>

            <View style={styles.buttonContainer}>
              <Button
                title="Nouveau scan"
                onPress={handleNewScan}
                style={styles.button}
              />
              <Button
                title="Retour"
                onPress={handleGoBack}
                variant="secondary"
                style={styles.button}
              />
            </View>
          </View>
        </View>
      </RoleBasedRoute>
    );
  }

  // Render the error screen
  if (scanned && result && !result.success) {
    return (
      <RoleBasedRoute allowedRoles={['admin', 'controlleur_fin_chaine']}>
        <View style={styles.resultContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={handleGoBack}
              style={styles.backButton}
            >
              <ArrowLeft color="#ffffff" size={24} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Contrôle Fin de Chaîne</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.errorContainer}>
            <CircleAlert size={64} color="#ef4444" />
            <Text style={styles.errorMessage}>{result.message}</Text>

            {result.message === 'Le packet est déjà en finition.' && (
              <Text style={styles.redirectMessage}>
                Redirection automatique dans quelques secondes...
              </Text>
            )}

            <View style={styles.buttonContainer}>
              <Button
                title="Nouveau scan"
                onPress={handleNewScan}
                style={styles.button}
              />
              <Button
                title="Retour"
                onPress={handleGoBack}
                variant="secondary"
                style={styles.button}
              />
            </View>
          </View>
        </View>
      </RoleBasedRoute>
    );
  }

  // Render the camera screen
  return (
    <RoleBasedRoute allowedRoles={['admin', 'controlleur_fin_chaine']}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={handleGoBack}
            style={styles.backButton}
          >
            <ArrowLeft color="#ffffff" size={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Contrôle Fin de Chaîne</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.cameraContainer}>
          <CameraView
            style={styles.camera}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          >
            <View style={styles.overlay}>
              <View style={styles.scanArea}>
                {isProcessing && (
                  <View style={styles.processingContainer}>
                    <ActivityIndicator size="large" color="#ffffff" />
                    <Text style={styles.processingText}>
                      Traitement en cours...
                    </Text>
                  </View>
                )}
              </View>
              <Text style={styles.instructions}>
                Placez le code QR du paquet dans le cadre
              </Text>
            </View>
          </CameraView>
        </View>
      </View>
    </RoleBasedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f172a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 48,
    paddingBottom: 16,
    backgroundColor: '#1e293b',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  placeholder: {
    width: 40,
  },
  cameraContainer: {
    flex: 1,
    alignItems: 'center',     // center horizontally
    justifyContent: 'flex-start', // align items from top
    paddingTop: 60,           // push it down from top
    backgroundColor: '#0f172a',
  },
  camera: {
    width: '80%',
    aspectRatio: 1, // Makes it a square
    borderRadius: 16,
    overflow: 'hidden',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#8b5cf6',
    borderRadius: 16,
    overflow: 'hidden',
  },
  processingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(139,92,246,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
  },
  instructions: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 24,
    textAlign: 'center',
  },
  resultContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  resultCard: {
    marginBottom: 16,
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  resultTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 12,
  },
  qrCode: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 16,
  },
  defautSection: {
    marginTop: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 12,
  },
  defautButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  defautButton: {
    flex: 1,
    marginHorizontal: 4,
  },
  // Button styles are now defined directly in activeDefautButton, activeDefectButton, and inactiveButton
  buttonIcon: {
    marginRight: 8,
  },
  piecesList: {
    marginTop: 8,
  },
  piecesTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1e293b',
    marginBottom: 12,
  },
  paginationInfo: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '400',
  },
  paginationControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 8,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  paginationButton: {
    minWidth: 100,
  },
  paginationText: {
    fontSize: 14,
    color: '#64748b',
    fontWeight: '500',
  },
  disabledButton: {
    opacity: 0.5,
  },
  pieceItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f1f5f9',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  pieceItemSelected: {
    backgroundColor: '#fef2f2',
    borderColor: '#fee2e2',
  },
  pieceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  pieceInfo: {
    flex: 1,
  },
  pieceNumber: {
    fontSize: 14,
    fontWeight: '600',
    color: '#64748b',
    marginBottom: 4,
  },
  pieceReference: {
    fontSize: 16,
    color: '#1e293b',
  },
  pieceStatusIndicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  pieceStatusOk: {
    backgroundColor: '#10b981',
  },
  pieceStatusDefaut: {
    backgroundColor: '#ef4444',
  },
  defautInputContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  defautHeaderRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  defautInputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
  },
  addDefautButton: {
    padding: 4,
  },
  addDefautButtonText: {
    color: '#8b5cf6',
    fontSize: 14,
    fontWeight: '500',
  },
  defautInputRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  defautInput: {
    marginTop: 0,
    marginBottom: 0,
  },
  removeDefautButton: {
    padding: 8,
    marginLeft: 8,
  },
  buttonContainer: {
    marginTop: 24,
  },
  button: {
    marginBottom: 8,
  },
  submitButton: {
    backgroundColor: '#ffa600',
    marginBottom: 8,
  },
  activeDefautButton: {
    backgroundColor: '#000440',
    borderWidth: 0,
  },
  activeDefectButton: {
    backgroundColor: '#ef4444',
    borderWidth: 0,
  },
  inactiveButton: {
    backgroundColor: '#d1d5db',
    borderWidth: 0,
  },
  successContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  successMessage: {
    fontSize: 18,
    fontWeight: '600',
    color: '#10b981',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  errorMessage: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 12,
  },
  redirectMessage: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 24,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f8fafc',
  },
  permissionText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    color: '#1e293b',
  },
  permissionButton: {
    width: '100%',
    marginBottom: 16,
  },
  cancelButton: {
    padding: 16,
  },
  cancelText: {
    color: '#64748b',
    fontSize: 16,
  },
});
