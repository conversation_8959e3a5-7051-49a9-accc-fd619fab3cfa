/**
 * Ultra-simple JWT decoder function that works in any JavaScript environment
 * This just extracts the payload part and tries to parse it as JSON
 */
export function decodeJWT(token: string): any {
  try {
    console.log('Decoding token:', token);

    // JWT token has 3 parts: header.payload.signature
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.error('Invalid JWT format, parts:', parts.length);
      return null;
    }

    // Get the payload (middle part)
    const payload = parts[1];
    console.log('Extracted payload:', payload);

    // Try to decode using a simple approach
    try {
      // Add padding if needed
      const paddedPayload = addBase64Padding(payload);

      // Use built-in atob if available
      if (typeof atob === 'function') {
        try {
          const jsonStr = atob(paddedPayload);
          console.log('Decoded with atob:', jsonStr);
          return JSON.parse(jsonStr);
        } catch (e) {
          console.error('atob decode failed:', e);
        }
      }

      // Fallback: Try to decode manually
      // This is a very basic approach that might not work for all tokens
      // but should work for simple ASCII payloads
      const jsonStr = b64DecodeUnicode(paddedPayload);
      console.log('Decoded with fallback:', jsonStr);
      return JSON.parse(jsonStr);
    } catch (e) {
      console.error('All decoding methods failed:', e);

      // Last resort: Just log the raw token parts for debugging
      console.log('Token parts:', {
        header: parts[0],
        payload: parts[1],
        signature: parts[2]
      });

      return null;
    }
  } catch (error) {
    console.error('Error in decodeJWT:', error);
    return null;
  }
}

/**
 * Add padding to base64 string if needed
 */
function addBase64Padding(str: string): string {
  // Convert URL-safe characters back to standard base64
  let output = str.replace(/-/g, '+').replace(/_/g, '/');

  // Add padding if needed
  switch (output.length % 4) {
    case 0:
      break; // No padding needed
    case 2:
      output += '==';
      break;
    case 3:
      output += '=';
      break;
    default:
      throw new Error('Invalid base64 string');
  }

  return output;
}

/**
 * Decode base64 to unicode string
 */
function b64DecodeUnicode(str: string): string {
  // Going backwards: from bytestream, to percent-encoding, to original string.
  try {
    return decodeURIComponent(
      Array.prototype.map
        .call(b64ToByteArray(str), (c: number) => {
          return '%' + ('00' + c.toString(16)).slice(-2);
        })
        .join('')
    );
  } catch (e) {
    // If decodeURIComponent fails, return a simple ASCII decode
    return b64ToAscii(str);
  }
}

/**
 * Convert base64 to byte array
 */
function b64ToByteArray(str: string): Uint8Array {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  const bytes = [];

  for (let i = 0, len = str.length; i < len; i += 4) {
    const encoded1 = chars.indexOf(str.charAt(i));
    const encoded2 = chars.indexOf(str.charAt(i + 1));
    const encoded3 = chars.indexOf(str.charAt(i + 2));
    const encoded4 = chars.indexOf(str.charAt(i + 3));

    const byte1 = (encoded1 << 2) | (encoded2 >> 4);
    const byte2 = ((encoded2 & 15) << 4) | (encoded3 >> 2);
    const byte3 = ((encoded3 & 3) << 6) | encoded4;

    bytes.push(byte1);

    if (encoded3 !== 64) {
      bytes.push(byte2);
    }
    if (encoded4 !== 64) {
      bytes.push(byte3);
    }
  }

  return new Uint8Array(bytes);
}

/**
 * Simple base64 to ASCII decoder
 */
function b64ToAscii(str: string): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
  let output = '';

  for (let i = 0; i < str.length; i += 4) {
    const c1 = chars.indexOf(str.charAt(i));
    const c2 = chars.indexOf(str.charAt(i + 1));
    const c3 = chars.indexOf(str.charAt(i + 2));
    const c4 = chars.indexOf(str.charAt(i + 3));

    output += String.fromCharCode(((c1 & 0x3F) << 2) | ((c2 & 0x30) >> 4));
    if (c3 !== 64) {
      output += String.fromCharCode(((c2 & 0x0F) << 4) | ((c3 & 0x3C) >> 2));
    }
    if (c4 !== 64) {
      output += String.fromCharCode(((c3 & 0x03) << 6) | c4);
    }
  }

  return output;
}
