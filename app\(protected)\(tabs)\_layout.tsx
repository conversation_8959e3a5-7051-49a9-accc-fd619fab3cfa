import React from 'react';
import { Tabs } from 'expo-router';
import { Boxes, HomeIcon, Scan, User, CheckCircle, Wrench } from 'lucide-react-native';
import { useAuth } from '@/context/AuthContext';
import { UserRole } from '@/types';

export default function TabLayout() {
  const { user } = useAuth();
  const userRole = user?.role || 'admin'; // Default to admin if no role is provided

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#ffa600',
        tabBarInactiveTintColor: '#64748b',
        tabBarStyle: {
          borderTopColor: '#e2e8f0',
          height: 70, // Increased height
          paddingBottom: 10, // Increased bottom padding
          paddingTop: 8,
          // Add safe area padding to ensure content isn't covered by phone borders
          marginBottom: 8,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '500',
          paddingBottom: 4, // Add padding to ensure text isn't too close to the bottom
        },
        headerShown: false,
      }}
    >
      {/* Orders tab - visible to all roles except restricted roles (but now visible to responsable_chaine) */}
      <Tabs.Screen
        name="index"
        options={{
          title: 'Ordres De Fabrication',
          tabBarIcon: ({ color, size }) => <Boxes size={size} color={color} />,
          // Hide the tab for restricted roles except responsable_chaine
          href: (userRole === 'ouvrier_machine' ||
                 userRole === 'controlleur_fin_chaine' ||
                 userRole === 'ouvrier_finnition') ? null : '/',
        }}
      />

      {/* Scan tab - visible to all roles except controlleur_fin_chaine and ouvrier_finnition */}
      <Tabs.Screen
        name="scan"
        options={{
          title: 'Scan',
          tabBarIcon: ({ color, size }) => <Scan size={size} color={color} />,
          // Hide the tab for controlleur_fin_chaine and ouvrier_finnition
          href: (userRole === 'controlleur_fin_chaine' ||
                 userRole === 'ouvrier_finnition') ? null : '/scan',
        }}
      />

      {/* Control Fin Chaine tab - only visible to controlleur_fin_chaine and admin */}
      <Tabs.Screen
        name="ctrl-fin-ch"
        options={{
          title: 'Contrôle Fin',
          tabBarIcon: ({ color, size }) => <CheckCircle size={size} color={color} />,
          // Hide the tab if the user doesn't have the right role
          href: (userRole === 'admin' || userRole === 'controlleur_fin_chaine')
            ? '/ctrl-fin-ch'
            : null,
        }}
      />

      {/* Finition tab - only visible to ouvrier_finnition and admin */}
      <Tabs.Screen
        name="finition"
        options={{
          title: 'Finition',
          tabBarIcon: ({ color, size }) => <Wrench size={size} color={color} />,
          // Hide the tab if the user doesn't have the right role
          href: (userRole === 'admin' || userRole === 'ouvrier_finnition')
            ? '/finition'
            : null,
        }}
      />

      {/* Profile tab - visible to all roles */}
      <Tabs.Screen
        name="profile"
        options={{
          title: 'Profile',
          tabBarIcon: ({ color, size }) => <User size={size} color={color} />,
        }}
      />
    </Tabs>
  );
}