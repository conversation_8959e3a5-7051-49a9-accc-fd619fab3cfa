import React from 'react';
import { StyleSheet, View, Text, ScrollView } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Wrench, ScanLine } from 'lucide-react-native';
import { useAuth } from '@/context/AuthContext';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import RoleBasedRoute from '@/components/auth/RoleBasedRoute';

export default function FinitionScreen() {
  const { user } = useAuth();

  const handleScanPress = () => {
    router.push('/scan/finition');
  };

  return (
    <RoleBasedRoute allowedRoles={['admin', 'ouvrier_finnition']}>
      <SafeAreaView style={styles.container} edges={['top']}>
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Finition</Text>
        </View>

        <ScrollView style={styles.content}>
          <View style={styles.introSection}>
            <Text style={styles.introTitle}>
              Marquer les paquets comme terminés
            </Text>
            <Text style={styles.introText}>
              Scannez chaque paquet individuellement pour marquer sa finition comme terminée. Cette action n'est possible que pour les paquets en finition.
            </Text>
          </View>

          <Card style={styles.actionCard}>
            <View style={styles.cardHeader}>
              <Wrench size={24} color="#10b981" />
              <Text style={styles.cardTitle}>Scanner un Paquet</Text> 
            </View>
            <Text style={styles.cardDescription}>
              Scannez le code QR d'un paquet pour marquer sa finition comme terminée
            </Text>
            <Button
              title="Scanner un Paquet"
              onPress={handleScanPress}
              style={styles.scanButton}
              icon={<ScanLine size={24} color="#ffffff" style={styles.buttonIcon} />}
            />
          </Card>
        </ScrollView>
      </SafeAreaView>
    </RoleBasedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    backgroundColor: '#ffffff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  introSection: {
    marginBottom: 24,
  },
  introTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 8,
  },
  introText: {
    fontSize: 16,
    color: '#64748b',
    lineHeight: 24,
  },
  actionCard: {
    marginBottom: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginLeft: 12,
  },
  cardDescription: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 16,
  },
  scanButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#10b981',
  },
  buttonIcon: {
    marginRight: 8,
  },
});
