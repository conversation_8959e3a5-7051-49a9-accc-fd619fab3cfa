import { ApiResponse } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';

const API_URL = 'https://racine-mode-tracker-backend.onrender.com/api'; // Update this to your actual backend URL

// Function to handle token expiration or invalidation
export async function handleTokenExpiration() {
  console.log('Token expired or invalid, logging out user');

  // Create a flag in memory to prevent multiple redirects
  if ((global as any).__isHandlingTokenExpiration) {
    console.log('Already handling token expiration, skipping duplicate call');
    return;
  }

  // Set the flag
  (global as any).__isHandlingTokenExpiration = true;

  try {
    // Clear authentication data immediately
    await Promise.all([
      AsyncStorage.removeItem('auth_token'),
      AsyncStorage.removeItem('user_data'),
      AsyncStorage.setItem('auth_error_message', 'Votre session a expiré. Veuillez vous reconnecter.')
    ]);

    console.log('Auth data cleared, forcing navigation to login page');

    // Force immediate navigation to login page
    router.replace('/login');

    // Reset the flag after a delay
    setTimeout(() => {
      (global as any).__isHandlingTokenExpiration = false;
    }, 1000);
  } catch (error) {
    console.error('Error during logout after token expiration:', error);

    // Force navigation to login even if there was an error clearing storage
    router.replace('/login');

    // Reset the flag after a delay
    setTimeout(() => {
      (global as any).__isHandlingTokenExpiration = false;
    }, 1000);
  }
}

type RequestMethod = 'GET' | 'POST' | 'PUT' | 'DELETE';

interface RequestOptions {
  method?: RequestMethod;
  body?: any;
  requiresAuth?: boolean;
}

export async function apiRequest<T>(
  endpoint: string,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> {
  const {
    method = 'GET',
    body = null,
    requiresAuth = true
  } = options;

  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (requiresAuth) {
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        return { success: false, error: 'Authentication required' };
      }
      headers['Authorization'] = `Bearer ${token}`; // Make sure this matches your backend's expected format
    }

    const config: RequestInit = {
      method,
      headers,
    };

    if (body) {
      config.body = JSON.stringify(body);
    }

    const response = await fetch(`${API_URL}${endpoint}`, config);

    // Check if response is ok before trying to parse JSON
    if (!response.ok) {
      // Check for authentication errors (401 Unauthorized)
      if (response.status === 401) {
        console.log('Received 401 Unauthorized response, token may be expired or invalid');
        // Handle token expiration - this will log out the user and redirect to login
        handleTokenExpiration();

        // Return immediately to prevent further processing
        return {
          success: false,
          error: 'Session expirée. Veuillez vous reconnecter.'
        };
      }

      // Try to parse as JSON first
      let errorMessage = `Error: ${response.status}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;

        // Check for token-related error messages in the response
        if (
          errorMessage.toLowerCase().includes('token') &&
          (errorMessage.toLowerCase().includes('expired') ||
           errorMessage.toLowerCase().includes('invalid') ||
           errorMessage.toLowerCase().includes('malformed'))
        ) {
          console.log('Token error detected in response:', errorMessage);
          handleTokenExpiration();
          return {
            success: false,
            error: 'Session expirée. Veuillez vous reconnecter.'
          };
        }
      } catch (parseError) {
        // If JSON parsing fails, try to get text content
        try {
          const textContent = await response.text();
          // Check if it's a 404 for barcode not found
          if (response.status === 404 && endpoint.includes('/scan')) {
            errorMessage = 'Code-barres non trouvé ou invalide';
          } else if (textContent.length < 100) {
            // Only use text content if it's reasonably short
            errorMessage = textContent;

            // Also check text content for token-related errors
            if (
              textContent.toLowerCase().includes('token') &&
              (textContent.toLowerCase().includes('expired') ||
               textContent.toLowerCase().includes('invalid') ||
               textContent.toLowerCase().includes('malformed'))
            ) {
              console.log('Token error detected in text response:', textContent);
              handleTokenExpiration();
              return {
                success: false,
                error: 'Session expirée. Veuillez vous reconnecter.'
              };
            }
          }
        } catch (textError) {
          // If all else fails, use the status code
          console.error('Failed to parse error response as text:', textError);
        }
      }

      return {
        success: false,
        error: errorMessage
      };
    }

    // If response is ok, parse as JSON
    try {
      const data = await response.json();
      return { success: true, data };
    } catch (parseError) {
      console.error('Failed to parse successful response as JSON:', parseError);
      return {
        success: false,
        error: 'Erreur lors de l\'analyse de la réponse du serveur'
      };
    }
  } catch (error) {
    console.error('API request failed:', error);

    // Check if the error might be related to token issues
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    // If the error mentions authentication or authorization, it might be a token issue
    if (
      requiresAuth &&
      (
        (errorMessage.toLowerCase().includes('auth') &&
         (errorMessage.toLowerCase().includes('failed') ||
          errorMessage.toLowerCase().includes('error'))) ||
        errorMessage.toLowerCase().includes('token') ||
        errorMessage.toLowerCase().includes('unauthorized') ||
        errorMessage.toLowerCase().includes('forbidden')
      )
    ) {
      console.log('Possible token-related error detected:', errorMessage);
      handleTokenExpiration();
      return {
        success: false,
        error: 'Session expirée. Veuillez vous reconnecter.'
      };
    }

    return {
      success: false,
      error: errorMessage
    };
  }
}

export async function sendScanResult(type: string, barcode: string): Promise<ApiResponse<any>> {
  return apiRequest('/scan', {
    method: 'POST',
    body: { type, barcode, timestamp: Date.now() },
  });
}

export async function fetchScanHistory(): Promise<ApiResponse<any>> {
  return apiRequest('/scan/history');
}