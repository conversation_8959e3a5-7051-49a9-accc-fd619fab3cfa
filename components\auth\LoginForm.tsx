import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, Alert } from 'react-native';
import { User, Lock, AlertCircle } from 'lucide-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { useAuth } from '@/context/AuthContext';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';

export default function LoginForm() {
  const { signIn, isLoading, error } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [sessionExpiredMessage, setSessionExpiredMessage] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState({
    email: '',
    password: '',
  });

  // Check for session expired message and clear any invalid tokens on component mount
  useEffect(() => {
    const initializeLoginForm = async () => {
      try {
        // Check for session expired message
        const message = await AsyncStorage.getItem('auth_error_message');
        if (message) {
          setSessionExpiredMessage(message);
          // Clear the message after retrieving it
          await AsyncStorage.removeItem('auth_error_message');
        }

        // Clear any existing tokens to prevent login loops
        // This ensures we start with a clean slate
        await AsyncStorage.removeItem('auth_token');
        await AsyncStorage.removeItem('user_data');

        console.log('LoginForm: Cleared any existing tokens to prevent login loops');
      } catch (error) {
        console.error('Error initializing login form:', error);
      }
    };

    initializeLoginForm();
  }, []);

  const validate = () => {
    const errors = {
      email: '',
      password: '',
    };

    if (!email.trim()) {
      errors.email = 'Email est requis';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      errors.email = 'Veuillez saisir une adresse e-mail valide';
    }

    if (!password) {
      errors.password = 'Le mot de passe est requis';
    }

    setValidationErrors(errors);

    return !errors.email && !errors.password;
  };

  const handleLogin = async () => {
    if (validate()) {
      // No need for try-catch here as the signIn function in AuthContext
      // already handles errors and sets the error state
      await signIn(email, password);
    }
  };

  return (
    <View style={styles.container}>
      {/* Display session expired message with priority */}
      {sessionExpiredMessage && (
        <View style={styles.sessionExpiredContainer}>
          <AlertCircle size={20} color="#ef4444" style={styles.errorIcon} />
          <Text style={styles.sessionExpiredText}>
            {sessionExpiredMessage}
          </Text>
        </View>
      )}

      {/* Display other auth errors if no session expired message */}
      {!sessionExpiredMessage && error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>
            {error === 'Invalid email or password'
              ? 'Email ou mot de passe incorrect. Veuillez réessayer.'
              : error === 'Authentication failed'
                ? 'Échec de l\'authentification. Veuillez réessayer.'
                : error}
          </Text>
        </View>
      )}

      <Input
        label="Email"
        placeholder="Entrer votre email"
        value={email}
        onChangeText={setEmail}
        autoCapitalize="none"
        keyboardType="email-address"
        error={validationErrors.email}
        leftIcon={<User size={20} color="#64748b" />}
      />

      <Input
        label="Mot De Passe"
        placeholder="Entrer votre mot de passe"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
        error={validationErrors.password}
        leftIcon={<Lock size={20} color="#64748b" />}
      />

      <Button
        title="Se connecter"
        onPress={handleLogin}
        isLoading={isLoading}
        style={styles.loginButton}
      />

      <TouchableOpacity
  style={styles.forgotPassword}
  onPress={() => Alert.alert('Mot de passe oublié', 'Contacter votre administration pour réinitialiser votre mot de passe.')}
>
  <Text style={styles.forgotPasswordText}>Mot de passe oublié?</Text>
</TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  errorContainer: {
    backgroundColor: '#fee2e2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  errorText: {
    color: '#b91c1c',
    fontSize: 14,
  },
  sessionExpiredContainer: {
    backgroundColor: '#fee2e2',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#fca5a5',
  },
  sessionExpiredText: {
    color: '#b91c1c',
    fontSize: 14,
    flex: 1,
    marginLeft: 8,
    fontWeight: '500',
  },
  errorIcon: {
    marginRight: 4,
  },
  loginButton: {
    marginTop: 8,
  },
  forgotPassword: {
    alignSelf: 'center',
    marginTop: 16,
    padding: 8,
  },
  forgotPasswordText: {
    color: '#3b82f6',
    fontSize: 14,
    fontWeight: '500',
  },
});