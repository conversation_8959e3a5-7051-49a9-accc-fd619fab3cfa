import React, { createContext, useContext, useState, useEffect } from 'react';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, AuthState, UserRole } from '@/types';
import { decodeJWT } from '@/utils/jwtDecode';
import { handleTokenExpiration } from '@/utils/api';

const API_URL = 'https://racine-mode-tracker-backend.onrender.com/api'; // Update this to your actual backend URL

interface AuthContextType extends AuthState {
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => void;
  handleExpiredToken: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    error: null,
  });

  // Function to validate token
  const validateToken = (token: string): boolean => {
    try {
      // Decode the token
      const decodedToken = decodeJWT(token);

      // Check if token is null (decoding failed)
      if (!decodedToken) {
        console.log('Token validation failed: Could not decode token');
        return false;
      }

      // Check if token has expiration time
      if (decodedToken.exp) {
        // Get current time in seconds
        const currentTime = Math.floor(Date.now() / 1000);

        // Check if token is expired
        if (decodedToken.exp < currentTime) {
          console.log('Token validation failed: Token expired');
          return false;
        }
      }

      // Check if token has required fields (userId, role)
      if (!decodedToken.userId || !decodedToken.role) {
        console.log('Token validation failed: Missing required fields');
        return false;
      }

      // Token passed all checks
      return true;
    } catch (error) {
      console.error('Error validating token:', error);
      return false;
    }
  };

  // Effect for periodic token validation
  useEffect(() => {
    // Only run if we have a user and token
    if (state.user && state.token) {
      // Set up interval to validate token every 5 minutes
      const intervalId = setInterval(() => {
        console.log('Running periodic token validation check');
        if (!validateToken(state.token!)) {
          console.log('Periodic token validation failed, logging out user');
          handleTokenExpiration();
          // Update state immediately
          setState({
            user: null,
            token: null,
            isLoading: false,
            error: null,
          });
        }
      }, 5 * 60 * 1000); // 5 minutes in milliseconds

      // Clean up interval on unmount
      return () => clearInterval(intervalId);
    }
  }, [state.user, state.token]);

  useEffect(() => {
    const loadUserData = async () => {
      try {
        const token = await AsyncStorage.getItem('auth_token');
        const userData = await AsyncStorage.getItem('user_data');

        if (token && userData) {
          // Validate the token
          if (!validateToken(token)) {
            console.log('Token validation failed, logging out user');
            handleTokenExpiration();
            setState({
              user: null,
              token: null,
              isLoading: false,
              error: null,
            });
            return;
          }

          // Parse the stored user data
          const parsedUserData = JSON.parse(userData);
          console.log('Loaded user data from storage:', parsedUserData);

          // If we have a token but no role in the stored user data, try to decode the token
          if (parsedUserData && (!parsedUserData.role || parsedUserData.role === 'undefined') && token) {
            console.log('No role found in stored user data, decoding token...');
            const decodedToken = decodeJWT(token);
            console.log('Decoded token in loadUserData:', decodedToken);

            if (decodedToken && decodedToken.role) {
              console.log('Found role in token:', decodedToken.role);
              // Update the user data with the role from the token
              parsedUserData.role = decodedToken.role;
              // Save the updated user data
              await AsyncStorage.setItem('user_data', JSON.stringify(parsedUserData));
            } else {
              console.log('No role found in token, setting default role to admin');
              // If we couldn't decode the token or no role was found, set a default role
              parsedUserData.role = 'admin';
              await AsyncStorage.setItem('user_data', JSON.stringify(parsedUserData));
            }
          }

          // Ensure the user has a role
          if (!parsedUserData.role || parsedUserData.role === 'undefined') {
            console.log('Setting default role to admin');
            parsedUserData.role = 'admin';
            await AsyncStorage.setItem('user_data', JSON.stringify(parsedUserData));
          }

          // Always try to decode the token to ensure we have the latest role information
          try {
            const decodedToken = decodeJWT(token);
            if (decodedToken && decodedToken.role) {
              // Update the role if it's different from what we have stored
              if (parsedUserData.role !== decodedToken.role) {
                console.log(`Updating role from ${parsedUserData.role} to ${decodedToken.role}`);
                parsedUserData.role = decodedToken.role;
                await AsyncStorage.setItem('user_data', JSON.stringify(parsedUserData));
              }
            } else {
              console.log('Could not extract role from token, keeping existing role:', parsedUserData.role);
            }
          } catch (e) {
            console.log('Error decoding token during state update:', e);
          }

          // Final check to ensure we have a valid role
          if (!parsedUserData.role || parsedUserData.role === 'undefined') {
            console.log('No valid role found after all checks, defaulting to admin');
            parsedUserData.role = 'admin';
            await AsyncStorage.setItem('user_data', JSON.stringify(parsedUserData));
          }

          setState({
            user: parsedUserData,
            token,
            isLoading: false,
            error: null,
          });
        } else {
          setState({ user: null, token: null, isLoading: false, error: null });
        }
      } catch (error) {
        console.error('Failed to load auth data:', error);
        setState({ user: null, token: null, isLoading: false, error: 'Failed to load user data' });
      }
    };

    loadUserData();
  }, []);

  const signIn = async (email: string, password: string) => {
    setState({ ...state, isLoading: true, error: null });

    try {
      const response = await fetch(`${API_URL}/users/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Authentication failed');
      }

      const { token } = data;

      // Decode the JWT token to get user information
      const decodedToken = decodeJWT(token);
      console.log('Decoded token in signIn:', decodedToken);

      // Create a user object with role information from the token if available
      let user: User;

      if (decodedToken) {
        // We successfully decoded the token
        user = {
          id: decodedToken.userId || 'temp-id',
          email,
          username: decodedToken.name || email.split('@')[0],
          role: decodedToken.role as UserRole || 'admin', // Get role from token
        };
      } else {
        // If we couldn't decode the token, create a basic user with admin role
        console.warn('Could not decode token, creating default user with admin role');
        user = {
          id: 'temp-id',
          email,
          username: email.split('@')[0],
          role: 'admin', // Default to admin if we can't decode the token
        };
      }

      console.log('Created user object:', user);

      await AsyncStorage.setItem('auth_token', token);
      await AsyncStorage.setItem('user_data', JSON.stringify(user));

      setState({
        user,
        token,
        isLoading: false,
        error: null,
      });

      // Redirect based on user role
      if (user.role === 'ouvrier_machine') {
        // Redirect ouvrier_machine directly to the scan page
        router.replace('/scan');
      } else if (user.role === 'controlleur_fin_chaine') {
        // Redirect controlleur_fin_chaine directly to the control fin chaine page
        router.replace('/ctrl-fin-ch');
      } else if (user.role === 'ouvrier_finnition') {
        // Redirect ouvrier_finnition directly to the finition page
        router.replace('/finition');
      } else {
        // All other roles go to the home page
        router.replace('/');
      }
    } catch (error) {
      console.error('Sign in error:', error);

      // Handle different types of errors
      let errorMessage = 'Sign in failed';

      if (error instanceof Error) {
        // Handle specific error messages
        if (error.message === 'Failed to fetch' || error.message.includes('Network request failed')) {
          errorMessage = 'Problème de connexion réseau. Veuillez vérifier votre connexion Internet.';
        } else {
          errorMessage = error.message;
        }
      }

      setState({
        ...state,
        isLoading: false,
        error: errorMessage,
      });
    }
  };

  const signOut = async () => {
    setState({ ...state, isLoading: true });

    try {
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_data');

      // Update state immediately
      setState({
        user: null,
        token: null,
        isLoading: false,
        error: null,
      });

      // Force navigation to login page
      router.replace('/login');
    } catch (error) {
      console.error('Sign out error:', error);
      setState({
        ...state,
        isLoading: false,
        error: 'Sign out failed',
      });

      // Even if there's an error, try to navigate to login
      router.replace('/login');
    }
  };

  // Add a public method to handle token expiration
  const handleExpiredToken = async () => {
    console.log('AuthContext: Handling expired token');

    try {
      await AsyncStorage.removeItem('auth_token');
      await AsyncStorage.removeItem('user_data');
      await AsyncStorage.setItem('auth_error_message', 'Votre session a expiré. Veuillez vous reconnecter.');

      // Update state immediately
      setState({
        user: null,
        token: null,
        isLoading: false,
        error: 'Session expirée',
      });

      // Force navigation to login page
      router.replace('/login');
    } catch (error) {
      console.error('Error handling expired token:', error);

      // Even if there's an error, update state and navigate
      setState({
        user: null,
        token: null,
        isLoading: false,
        error: 'Session expirée',
      });

      router.replace('/login');
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        signIn,
        signOut,
        handleExpiredToken,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}