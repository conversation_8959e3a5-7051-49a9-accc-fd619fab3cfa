import React, { useState, useCallback, useEffect } from 'react';
import {
  ScrollView,
  RefreshControl,
  StyleSheet,
  View,
  Text,
  ActivityIndicator,
  TouchableOpacity
} from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { Package, ArrowLeft, <PERSON>, Scan, Shirt } from 'lucide-react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { useAuth } from '@/context/AuthContext';
import { apiRequest } from '@/utils/api';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import OrderTimeline from '@/components/order/OrderTimeline';

interface Piece {
  _id: string;
  reference: string;
  status?: string;
  defaut?: string[];
}

interface Packet {
  _id: string;
  qrCode: string;
  status: string;
  pieces: Piece[];
  scans: Array<{
    type: string;
    time: string;
    user?: string;
  }>;
}

interface Colis {
  _id: string;
  numeroColis: number;
  coloris: string;
  tailles: string;
  quantite: number;
  status: string;
  qrCode: string;
  packets: Packet[];
  scans: Array<{
    type: string;
    time: string;
    user?: string;
  }>;
}

interface Article {
  _id: string;
  name: string;
  reference: string;
  model: any;
}

interface Order {
  _id: string;
  orderNumber: string;
  status: 'pending' | 'in_progress' | 'finishing' | 'completed' | 'canceled';
  totalPieces: number;
  article: Article;
  colis: Colis[];
  createdAt: string;
  scans: Array<{
    type: 'EM' | 'SM' | 'SF';
    time: string;
    user?: string;
  }>;
}

interface OrderTracking {
  order: {
    id: string;
    orderNumber: string;
    status: string;
    bloquer: boolean;
    launchDate: string;
    article: Article;
  };
  timeline: {
    orderStartTime: string | null;
    orderFinMontage: string | null;
    orderFinished: string | null;
    montagePhase: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
    finitionPhase: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
    totalDuration: {
      start: string;
      end: string;
      durationMinutes: number;
    } | null;
  };
  packets: {
    totals: {
      enCours: number;
      enAttente: number;
      done: number;
      retouche: number;
      finnishing: number;
      total: number;
    };
    scannedCount: {
      debutGM: number;
      finGM: number;
    };
    productivity: {
      completionRate: number;
      averageTimePerPacket: number | null;
    };
  };
  dailyActivity: {
    debutGM: Array<{
      date: string;
      totalCount: number;
      hours: Array<{
        hour: string;
        count: number;
        scans: Array<{
          time: string;
          packetId: string;
          user: string;
        }>;
      }>;
      scans: Array<{
        time: string;
        packetId: string;
        user: string;
      }>;
    }>;
    finGM: Array<{
      date: string;
      totalCount: number;
      hours: Array<{
        hour: string;
        count: number;
        scans: Array<{
          time: string;
          packetId: string;
          user: string;
        }>;
      }>;
      scans: Array<{
        time: string;
        packetId: string;
        user: string;
      }>;
    }>;
    summary: {
      totalDaysActive: number;
      peakDayDebutGM: {
        date: string;
        totalCount: number;
      } | null;
      peakDayFinGM: {
        date: string;
        totalCount: number;
      } | null;
      totalDebutGMDays: number;
      totalFinGMDays: number;
    };
  };
  scanDetails: {
    totalDebutGMScans: number;
    totalFinGMScans: number;
    firstDebutGM: string | null;
    lastFinGM: string | null;
  };
}

export default function OrderDetails() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user } = useAuth();
  const [order, setOrder] = useState<Order | null>(null);
  const [trackingData, setTrackingData] = useState<OrderTracking | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check if user has permission to scan packets
  const canScanPackets = user?.role !== 'responsable_chaine';

  const translateStatus = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'in_progress':
        return 'En cours';
      case 'finishing':
        return 'Finition';
      case 'completed':
        return 'Terminé';
      case 'canceled':
        return 'Annulé';
      default:
        return status;
    }
  };

  const translateColisStatus = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'in_progress':
        return 'En cours';
      case 'retouche':
        return 'Retouche';
      case 'finnishing':
        return 'Finition';
      case 'completed':
        return 'Terminé';
      default:
        return status;
    }
  };

  const translatePacketStatus = (status: string) => {
    switch (status) {
      case 'pending':
        return 'En attente';
      case 'in_progress':
        return 'En cours';
      case 'retouche':
        return 'Retouche';
      case 'finnishing':
        return 'Finition';
      case 'completed':
        return 'Terminé';
      default:
        return status;
    }
  };

  const fetchOrderDetails = useCallback(async (showLoadingSpinner = true) => {
    if (showLoadingSpinner) {
      setIsLoading(true);
    }

    try {
      setError(null);

      // Fetch basic order details
      const orderResponse = await apiRequest<Order>(`/orders/${id}`);

      if (orderResponse.success && orderResponse.data) {
        setOrder(orderResponse.data);

        // For responsable_chaine, also fetch tracking data
        if (user?.role === 'responsable_chaine') {
          const trackingResponse = await apiRequest<OrderTracking>(`/orders/${id}/tracking`);
          if (trackingResponse.success && trackingResponse.data) {
            setTrackingData(trackingResponse.data);
          }
        }
      } else {
        setError(orderResponse.error || 'Failed to load order details');
      }
    } catch (err) {
      console.error('Error fetching order:', err);
      setError('Failed to load order details');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, [id, user?.role]);

  useEffect(() => {
    fetchOrderDetails();
  }, [fetchOrderDetails]);

  const handleScanPacket = () => {
    router.push(`/scan/packet?orderId=${id}`);
  };

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return '#f59e0b';
      case 'in_progress':
        return '#3b82f6';
      case 'finishing':
        return '#8b5cf6';
      case 'completed':
        return '#10b981';
      case 'canceled':
        return '#ef4444';
      default:
        return '#64748b';
    }
  };

  const getColisStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b';
      case 'in_progress':
        return '#3b82f6';
      case 'retouche':
        return '#f97316';
      case 'finnishing':
        return '#8b5cf6';
      case 'completed':
        return '#10b981';
      default:
        return '#64748b';
    }
  };

  const getPacketStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#f59e0b';
      case 'in_progress':
        return '#3b82f6';
      case 'retouche':
        return '#f97316';
      case 'finnishing':
        return '#8b5cf6';
      case 'completed':
        return '#10b981';
      default:
        return '#64748b';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading && !isRefreshing) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#3b82f6" />
        <Text style={styles.loadingText}>Chargement des détails...</Text>
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
        >
          <ArrowLeft size={24} color="#1e293b" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Détails OF</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={isRefreshing}
            onRefresh={() => {
              setIsRefreshing(true);
              fetchOrderDetails(false);
            }}
            colors={['#3b82f6']}
            tintColor="#3b82f6"
          />
        }
      >
        {error ? (
          <Card style={styles.errorCard}>
            <Text style={styles.errorText}>{error}</Text>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => fetchOrderDetails()}
            >
              <Text style={styles.retryText}>Réessayer</Text>
            </TouchableOpacity>
          </Card>
        ) : order ? (
          <View style={styles.orderContent}>
            <Card style={styles.orderCard}>
              <View style={styles.orderHeader}>
                <Text style={styles.orderNumber}>OF #{order.orderNumber}</Text>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor(order.status) }
                ]}>
                  <Text style={styles.statusText}>
                    {translateStatus(order.status)}
                  </Text>
                </View>
              </View>

              <View style={styles.articleDetails}>

                <Text style={styles.articleRef}>
                  <Shirt size={20} /> {order.article.model}
                </Text>
              </View>

              <View style={styles.statsRow}>
                <View style={styles.statItem}>
                  <Package size={16} color="#64748b" />
                  <Text style={styles.statText}>
                    {order.totalPieces} pièces
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Package size={16} color="#8b5cf6" />
                  <Text style={styles.statText}>
                    {order.colis.length} colis
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Clock size={16} color="#64748b" />
                  <Text style={styles.statText}>
                    {formatDate(order.createdAt)}
                  </Text>
                </View>
              </View>
            </Card>

            {canScanPackets && (
              <View style={styles.actionSection}>
                <Button
                  title="Scanner Packet"
                  onPress={handleScanPacket}
                  icon={<Scan size={20} color="#ffffff" style={styles.buttonIcon} />}
                />
              </View>
            )}

            {/* Show timeline for responsable_chaine */}
            {user?.role === 'responsable_chaine' && trackingData && (
              <OrderTimeline
                dailyActivity={trackingData.dailyActivity}
                packets={trackingData.packets}
                timeline={trackingData.timeline}
              />
            )}

            {/* Hide colis details for responsable_chaine */}
            {user?.role !== 'responsable_chaine' && (
              <View style={styles.colisSection}>
              <Text style={styles.sectionTitle}>Colis ({order.colis.length})</Text>
              {order.colis.map((colis) => (
                <Card key={colis._id} style={styles.colisCard}>
                  <View style={styles.colisHeader}>
                    <Text style={styles.colisId}>
                      Colis #{colis.numeroColis}
                    </Text>
                    <View style={styles.colisInfo}>
                      <Text style={styles.colisDetail}>
                        {colis.coloris} - {colis.tailles}
                      </Text>
                      <Text style={styles.colisQuantity}>
                        Qté: {colis.quantite}
                      </Text>
                    </View>
                  </View>

                  <View style={styles.colisStatus}>
                    <Text style={styles.statusLabel}>Status:</Text>
                    <View style={[
                      styles.statusBadgeSmall,
                      { backgroundColor: getColisStatusColor(colis.status) }
                    ]}>
                      <Text style={styles.statusTextSmall}>
                        {translateColisStatus(colis.status)}
                      </Text>
                    </View>
                  </View>

                  {/* Colis Scans */}
                  {colis.scans.length > 0 && (
                    <View style={styles.scansSection}>
                      <Text style={styles.scansTitle}>Scans Colis:</Text>
                      {colis.scans.map((scan, index) => (
                        <View key={index} style={styles.scanItem}>
                          <Text style={styles.scanType}>{scan.type}</Text>
                          <Text style={styles.scanTime}>
                            {formatDate(scan.time)}
                          </Text>
                        </View>
                      ))}
                    </View>
                  )}

                  {/* Packets in this Colis */}
                  <View style={styles.packetsInColis}>
                    <Text style={styles.packetsTitle}>
                      Paquets ({colis.packets.length})
                    </Text>
                    {colis.packets.map((packet) => (
                      <View key={packet._id} style={styles.packetItem}>
                        <View style={styles.packetHeader}>
                          <Text style={styles.packetId}>
                            Paquet #{packet.qrCode}
                          </Text>
                          <Text style={styles.piecesCount}>
                            {packet.pieces.length} pièces
                          </Text>
                        </View>

                        <View style={styles.packetStatus}>
                          <Text style={styles.statusLabel}>Status:</Text>
                          <View style={[
                            styles.statusBadgeSmall,
                            { backgroundColor: getPacketStatusColor(packet.status) }
                          ]}>
                            <Text style={styles.statusTextSmall}>
                              {translatePacketStatus(packet.status)}
                            </Text>
                          </View>
                        </View>

                        {packet.scans.length > 0 && (
                          <View style={styles.packetScans}>
                            {packet.scans.map((scan, index) => (
                              <View key={index} style={styles.scanItemSmall}>
                                <Text style={styles.scanTypeSmall}>{scan.type}</Text>
                                <Text style={styles.scanTimeSmall}>
                                  {formatDate(scan.time)}
                                </Text>
                              </View>
                            ))}
                          </View>
                        )}
                      </View>
                    ))}
                  </View>
                </Card>
              ))}
              </View>
            )}
          </View>
        ) : null}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e2e8f0',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
  },
  backButton: {
    padding: 8,
    marginLeft: -8,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#64748b',
  },
  errorCard: {
    margin: 16,
    padding: 16,
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    color: '#ef4444',
    marginBottom: 12,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#3b82f6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  retryText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  orderContent: {
    padding: 16,
  },
  orderCard: {
    marginBottom: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#ffffff',
  },
  articleDetails: {
    marginBottom: 16,
  },
  articleName: {
    fontSize: 19,
    color: '#1e293b',
    marginBottom: 4,
  },
  articleRef: {
    fontSize: 16,
    color: '#64748b',

  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    marginLeft: 8,
    fontSize: 16,
    color: '#64748b',
  },
  actionSection: {
    marginBottom: 24,
  },
  buttonIcon: {
    marginRight: 8,
  },
  colisSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 12,
  },
  colisCard: {
    marginBottom: 16,
    padding: 16,
  },
  colisHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  colisId: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
  },
  colisInfo: {
    alignItems: 'flex-end',
  },
  colisDetail: {
    fontSize: 14,
    color: '#64748b',
    marginBottom: 2,
  },
  colisQuantity: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1e293b',
  },
  colisStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  statusLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#64748b',
  },
  statusBadgeSmall: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusTextSmall: {
    fontSize: 12,
    fontWeight: '500',
    color: '#ffffff',
  },
  scansSection: {
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    paddingTop: 12,
    marginBottom: 12,
  },
  scansTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 8,
  },
  scanItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  scanType: {
    fontSize: 14,
    color: '#1e293b',
  },
  scanTime: {
    fontSize: 12,
    color: '#64748b',
  },
  packetsInColis: {
    borderTopWidth: 1,
    borderTopColor: '#e2e8f0',
    paddingTop: 12,
  },
  packetsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 12,
  },
  packetItem: {
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#8b5cf6',
  },
  packetHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  packetId: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1e293b',
  },
  piecesCount: {
    fontSize: 12,
    color: '#64748b',
  },
  packetStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  packetScans: {
    gap: 4,
  },
  scanItemSmall: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 2,
  },
  scanTypeSmall: {
    fontSize: 12,
    color: '#64748b',
  },
  scanTimeSmall: {
    fontSize: 10,
    color: '#94a3b8',
  },
});