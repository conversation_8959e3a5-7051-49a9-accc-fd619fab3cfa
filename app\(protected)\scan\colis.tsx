import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, CircleCheck, CircleAlert, Package } from 'lucide-react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';

import { useAuth } from '@/context/AuthContext';
import { apiRequest } from '@/utils/api';
import { playBeepSound } from '@/utils/sound';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';

interface ColisResponse {
  _id: string;
  numeroColis: number;
  coloris: string;
  tailles: string;
  quantite: number;
  status: string;
  order: {
    orderNumber: string;
    status: string;
  };
  packets: Array<{
    _id: string;
    qrCode: string;
    status: string;
    pieces: Array<{
      _id: string;
      reference: string;
      status?: string;
      defaut?: string[];
    }>;
  }>;
  qrCode: string;
}

export default function ColisScanScreen() {
  const { user } = useAuth();
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    colis?: ColisResponse;
  } | null>(null);

  if (!permission) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Nous avons besoin de la permission d'utiliser la caméra pour scanner les codes QR
        </Text>
        <Button
          title="Accorder la permission"
          onPress={requestPermission}
          style={styles.permissionButton}
        />
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.cancelButton}
        >
          <Text style={styles.cancelText}>Annuler</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleBarCodeScanned = async ({ data }: { data: string }) => {
    if (scanned || isProcessing) return;

    setScanned(true);
    setIsProcessing(true);

    try {
      console.log('Scanning colis QR code:', data);

      // Get the colis info using the get-by-qr endpoint
      const response = await apiRequest<ColisResponse>('/colis/get-by-qr', {
        method: 'POST',
        body: {
          qr: data,
        },
      });

      console.log('Colis scan response:', response);

      if (response.success && response.data) {
        // Trigger success haptic feedback
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Play beep sound
        playBeepSound();

        setResult({
          success: true,
          message: 'Colis trouvé avec succès',
          colis: response.data,
        });
      } else {
        // Handle specific error messages from the server
        let errorMessage = response.error || 'Échec du scan';

        // Check for specific error messages
        if (errorMessage.includes('Colis not found') || errorMessage.includes('404')) {
          errorMessage = 'Colis non trouvé. Veuillez vérifier le code QR.';
        }

        setResult({
          success: false,
          message: errorMessage,
        });

        console.log('Colis scan error:', response.error);
      }
    } catch (error) {
      console.error('Error scanning colis:', error);
      setResult({
        success: false,
        message: 'Une erreur est survenue lors du scan. Veuillez réessayer.',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNewScan = () => {
    setScanned(false);
    setResult(null);
  };

  const handleGoBack = () => {
    router.back();
  };

  const handleControlColis = () => {
    if (result?.colis) {
      router.push({
        pathname: '/control-colis',
        params: { colisId: result.colis._id }
      });
    }
  };

  // Show result screen
  if (result) {
    if (result.success && result.colis) {
      return (
        <View style={styles.resultContainer}>
          <View style={styles.resultHeader}>
            <TouchableOpacity
              onPress={handleGoBack}
              style={styles.backButton}
            >
              <ArrowLeft color="#ffffff" size={24} />
            </TouchableOpacity>
            <Text style={styles.resultHeaderTitle}>Colis Scanné</Text>
            <View style={styles.placeholder} />
          </View>

          <ScrollView style={styles.resultContent}>
            <Card style={styles.successCard}>
              <View style={styles.successHeader}>
                <CircleCheck size={64} color="#10b981" />
                <Text style={styles.successMessage}>{result.message}</Text>
              </View>

              <View style={styles.colisInfo}>
                <Text style={styles.colisTitle}>Colis N°{result.colis.numeroColis}</Text>
                <Text style={styles.colisDetail}>Coloris: {result.colis.coloris}</Text>
                <Text style={styles.colisDetail}>Tailles: {result.colis.tailles}</Text>
                <Text style={styles.colisDetail}>Quantité: {result.colis.quantite}</Text>
                <Text style={styles.colisDetail}>Status: {result.colis.status}</Text>
                <Text style={styles.colisDetail}>OF: {result.colis.order.orderNumber}</Text>
                <Text style={styles.colisDetail}>Paquets: {result.colis.packets.length}</Text>
              </View>

              <View style={styles.buttonContainer}>
                <Button
                  title="Contrôler ce Colis"
                  onPress={handleControlColis}
                  style={styles.controlButton}
                />
                <Button
                  title="Nouveau scan"
                  onPress={handleNewScan}
                  variant="outline"
                  style={styles.button}
                />
                <Button
                  title="Retour"
                  onPress={handleGoBack}
                  variant="secondary"
                  style={styles.button}
                />
              </View>
            </Card>
          </ScrollView>
        </View>
      );
    } else {
      // Error result
      return (
        <View style={styles.resultContainer}>
          <View style={styles.resultHeader}>
            <TouchableOpacity
              onPress={handleGoBack}
              style={styles.backButton}
            >
              <ArrowLeft color="#ffffff" size={24} />
            </TouchableOpacity>
            <Text style={styles.resultHeaderTitle}>Erreur de scan</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.resultContent}>
            <View style={styles.errorContainer}>
              <CircleAlert size={64} color="#ef4444" />
              <Text style={styles.errorMessage}>{result.message}</Text>

              <View style={styles.buttonContainer}>
                <Button
                  title="Nouveau scan"
                  onPress={handleNewScan}
                  style={styles.button}
                />
                <Button
                  title="Retour"
                  onPress={handleGoBack}
                  variant="secondary"
                  style={styles.button}
                />
              </View>
            </View>
          </View>
        </View>
      );
    }
  }

  // Render the camera screen
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          onPress={handleGoBack}
          style={styles.backButton}
        >
          <ArrowLeft color="#ffffff" size={24} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Scanner Colis</Text>
        <View style={styles.placeholder} />
      </View>
      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
        >
          <View style={styles.overlay}>
            <View style={styles.scanArea}>
              {isProcessing && (
                <View style={styles.processingContainer}>
                  <ActivityIndicator size="large" color="#ffffff" />
                  <Text style={styles.processingText}>
                    Traitement en cours...
                  </Text>
                </View>
              )}
            </View>
            <View style={styles.scanInstructions}>
              <Text style={styles.instructionText}>
                Alignez le code QR du colis dans le cadre
              </Text>
            </View>
          </View>
        </CameraView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000440',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#000440',
    paddingTop: 50,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  placeholder: {
    width: 40,
  },
  cameraContainer: {
    flex: 1,
    margin: 20,
    borderRadius: 20,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#ffffff',
    borderRadius: 20,
    backgroundColor: 'transparent',
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingContainer: {
    alignItems: 'center',
  },
  processingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 10,
    textAlign: 'center',
  },
  scanInstructions: {
    position: 'absolute',
    bottom: 100,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  instructionText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 10,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f8fafc',
  },
  permissionText: {
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
    marginBottom: 20,
  },
  permissionButton: {
    marginBottom: 10,
  },
  cancelButton: {
    padding: 10,
  },
  cancelText: {
    color: '#64748b',
    fontSize: 16,
  },
  resultContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  resultHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#000440',
    paddingTop: 50,
  },
  resultHeaderTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  resultContent: {
    flex: 1,
    padding: 16,
  },
  successCard: {
    padding: 20,
  },
  successHeader: {
    alignItems: 'center',
    marginBottom: 20,
  },
  successMessage: {
    fontSize: 18,
    fontWeight: '600',
    color: '#10b981',
    marginTop: 12,
    textAlign: 'center',
  },
  colisInfo: {
    marginBottom: 20,
  },
  colisTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#1e293b',
    marginBottom: 12,
  },
  colisDetail: {
    fontSize: 16,
    color: '#64748b',
    marginBottom: 6,
  },
  buttonContainer: {
    gap: 12,
  },
  controlButton: {
    backgroundColor: '#8b5cf6',
  },
  button: {
    marginBottom: 8,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorMessage: {
    fontSize: 18,
    color: '#ef4444',
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 24,
  },
});
