import { Audio } from 'expo-av';

// Load the sound file
let beepSound: Audio.Sound | null = null;

/**
 * Loads the scanner beep sound
 */
export const loadSounds = async () => {
  try {
    // Unload any existing sound to prevent memory leaks
    if (beepSound) {
      await beepSound.unloadAsync();
    }
    
    // Load the beep sound
    const { sound } = await Audio.Sound.createAsync(
      require('../assets/audio/scannerBeep.mp3')
    );
    
    beepSound = sound;
    console.log('Beep sound loaded successfully');
  } catch (error) {
    console.error('Failed to load beep sound:', error);
  }
};

/**
 * Plays the scanner beep sound
 */
export const playBeepSound = async () => {
  try {
    // If sound is not loaded yet, load it
    if (!beepSound) {
      await loadSounds();
    }
    
    // Play the sound
    if (beepSound) {
      // Make sure the sound is at the beginning
      await beepSound.setPositionAsync(0);
      await beepSound.playAsync();
    }
  } catch (error) {
    console.error('Failed to play beep sound:', error);
  }
};

/**
 * Unloads all sounds to prevent memory leaks
 */
export const unloadSounds = async () => {
  try {
    if (beepSound) {
      await beepSound.unloadAsync();
      beepSound = null;
    }
  } catch (error) {
    console.error('Failed to unload sounds:', error);
  }
};
