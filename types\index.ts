export type UserRole = 'admin' | 'responsable_chaine' | 'controlleur_fin_chaine' | 'ouvrier_machine' | 'ouvrier_finnition';

export interface User {
  id: string;
  username: string;
  email: string;
  role?: UserRole;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

export type ScanType = 'general' | 'order' | 'packet';

export interface ScanResult {
  id: string;
  type: ScanType;
  barcode: string;
  timestamp: number;
  success: boolean;
  message?: string;
}

export interface ScanHistory {
  results: ScanResult[];
  isLoading: boolean;
  error: string | null;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}