import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator
} from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft, CircleCheck, CircleAlert } from 'lucide-react-native';
import { CameraView, useCameraPermissions } from 'expo-camera';
import * as Haptics from 'expo-haptics';

import { useAuth } from '@/context/AuthContext';
import { apiRequest } from '@/utils/api';
import { playBeepSound } from '@/utils/sound';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import RoleBasedRoute from '@/components/auth/RoleBasedRoute';

interface ScanResponse {
  message: string;
  packet: {
    qrCode: string;
    status: string;
  };
}

export default function FinitionScanScreen() {
  const { user } = useAuth();
  const [permission, requestPermission] = useCameraPermissions();
  const [scanned, setScanned] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    packetCode?: string;
  } | null>(null);

  if (!permission) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color="#3b82f6" />
      </View>
    );
  }

  if (!permission.granted) {
    return (
      <View style={styles.permissionContainer}>
        <Text style={styles.permissionText}>
          Nous avons besoin de votre permission pour utiliser la caméra
        </Text>
        <Button
          title="Autoriser la caméra"
          onPress={requestPermission}
          style={styles.permissionButton}
        />
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.cancelButton}
        >
          <Text style={styles.cancelText}>Annuler</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handleBarCodeScanned = async ({ data }: { data: string }) => {
    if (scanned || isProcessing) return;

    setScanned(true);
    setIsProcessing(true);

    try {
      console.log('Scanning finition packet barcode:', data);

      const response = await apiRequest<ScanResponse>('/packets/scan-finishing', {
        method: 'POST',
        body: {
          barcode: data,
        },
      });

      console.log('Finition packet scan response:', response);

      if (response.success && response.data) {
        // Trigger success haptic feedback
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        // Play beep sound
        playBeepSound();

        setResult({
          success: true,
          message: response.data.message,
          packetCode: response.data.packet.qrCode,
        });

        // Auto-refresh after 2 seconds to allow for new scan
        setTimeout(() => {
          handleNewScan();
        }, 2000);
      } else {
        // Handle specific error messages from the server
        let errorMessage = response.error || 'Échec du scan';

        // Check for specific error messages
        if (errorMessage.includes('Packet not found') || errorMessage.includes('404')) {
          errorMessage = 'Paquet non trouvé. Veuillez vérifier le code-barres.';
        } else if (errorMessage.includes('encore en')) {
          // Handle "Le packet est encore en [status]" message
          errorMessage = response.error || errorMessage;
        } else if (errorMessage.includes('Invalid barcode format')) {
          errorMessage = 'Format de code-barres invalide. Format attendu: orderNumber/C1/P1';
        }

        setResult({
          success: false,
          message: errorMessage,
        });

        console.log('Finition packet scan error:', response.error);
      }
    } catch (error) {
      console.error('Error scanning packet for finition:', error);
      setResult({
        success: false,
        message: 'Une erreur est survenue lors du scan. Veuillez réessayer.',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNewScan = () => {
    setScanned(false);
    setResult(null);
  };

  const handleGoBack = () => {
    router.back();
  };

  // Render the result screen
  if (scanned && result) {
    return (
      <RoleBasedRoute allowedRoles={['admin', 'ouvrier_finnition']}>
        <View style={styles.resultContainer}>
          <View style={styles.header}>
            <TouchableOpacity
              onPress={handleGoBack}
              style={styles.backButton}
            >
              <ArrowLeft color="#ffffff" size={24} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Paquet Terminé</Text>
            <View style={styles.placeholder} />
          </View>

          <View style={styles.resultContent}>
            <Card style={styles.resultCard}>
              <View style={styles.resultIconContainer}>
                {result.success ? (
                  <CircleCheck size={64} color="#10b981" />
                ) : (
                  <CircleAlert size={64} color="#ef4444" />
                )}
              </View>

              <Text style={[
                styles.resultMessage,
                result.success ? styles.successText : styles.errorText
              ]}>
                {result.message}
              </Text>

              {result.success && result.packetCode && (
                <Text style={styles.orderNumber}>
                  Paquet: {result.packetCode}
                </Text>
              )}

              <View style={styles.buttonContainer}>
                <Button
                  title="Nouveau scan"
                  onPress={handleNewScan}
                  style={styles.button}
                />
                <Button
                  title="Retour"
                  onPress={handleGoBack}
                  variant="secondary"
                  style={styles.button}
                />
              </View>
            </Card>
          </View>
        </View>
      </RoleBasedRoute>
    );
  }

  // Render the camera screen
  return (
    <RoleBasedRoute allowedRoles={['admin', 'ouvrier_finnition']}>
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={handleGoBack}
            style={styles.backButton}
          >
            <ArrowLeft color="#ffffff" size={24} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Scanner Paquet</Text>
          <View style={styles.placeholder} />
        </View>
        <View style={styles.cameraContainer}>
          <CameraView
            style={styles.camera}
            onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          >
            <View style={styles.overlay}>
              <View style={styles.scanArea}>
                {isProcessing && (
                  <View style={styles.processingContainer}>
                    <ActivityIndicator size="large" color="#ffffff" />
                    <Text style={styles.processingText}>
                      Traitement en cours...
                    </Text>
                  </View>
                )}
              </View>
              <Text style={styles.instructions}>
                Placez le code QR du paquet dans le cadre
              </Text>
            </View>
          </CameraView>
        </View>
      </View>
    </RoleBasedRoute>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f172a',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 48,
    paddingBottom: 16,
    backgroundColor: '#1e293b',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#ffffff',
  },
  placeholder: {
    width: 40,
  },
  cameraContainer: {
    flex: 1,
    alignItems: 'center',     // center horizontally
    justifyContent: 'flex-start', // align items from top
    paddingTop: 60,           // push it down from top
    backgroundColor: '#0f172a',
  },
  camera: {
    width: '80%',
    aspectRatio: 1, // Makes it a square
    borderRadius: 16,
    overflow: 'hidden',
  },
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanArea: {
    width: 250,
    height: 250,
    borderWidth: 2,
    borderColor: '#10b981',
    borderRadius: 16,
    overflow: 'hidden',
  },
  processingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(16,185,129,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  processingText: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 16,
  },
  instructions: {
    color: '#ffffff',
    fontSize: 16,
    marginTop: 24,
    textAlign: 'center',
  },
  resultContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  resultContent: {
    flex: 1,
    padding: 16,
    justifyContent: 'center',
  },
  resultCard: {
    padding: 24,
  },
  resultIconContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  resultMessage: {
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 16,
  },
  successText: {
    color: '#10b981',
  },
  errorText: {
    color: '#ef4444',
  },
  orderNumber: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1e293b',
    textAlign: 'center',
  },
  buttonContainer: {
    width: '100%',
    marginTop: 24,
  },
  button: {
    marginBottom: 8,
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f8fafc',
  },
  permissionText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 24,
    color: '#1e293b',
  },
  permissionButton: {
    width: '100%',
    marginBottom: 16,
  },
  cancelButton: {
    padding: 16,
  },
  cancelText: {
    color: '#64748b',
    fontSize: 16,
  },
});
