{"name": "racine-mode-scanner", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "build:android": "eas build -p android --profile preview", "dev": "expo start"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@react-native-async-storage/async-storage": "^2.0.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.0.14", "expo": "^53.0.0", "expo-av": "^15.1.4", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-font": "~13.3.1", "expo-haptics": "~14.1.3", "expo-image": "~2.1.7", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.4", "expo-router": "~5.0.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.5", "expo-updates": "~0.28.12", "expo-web-browser": "~14.1.6", "jwt-decode": "^4.0.0", "lucide-react-native": "^0.475.0", "ngrok": "^5.0.0-beta.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "tslib": "^2.8.1", "typescript": "~5.8.3"}, "engines": {"node": ">=18", "npm": ">=9"}, "private": true}