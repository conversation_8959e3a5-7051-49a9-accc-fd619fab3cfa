import { useAuth } from '@/context/AuthContext';
import { Slot, Redirect } from 'expo-router';
import React, { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { decodeJWT } from '@/utils/jwtDecode';

export default function AuthLayout() {
  const { user, isLoading, handleExpiredToken } = useAuth();
  const [isTokenValid, setIsTokenValid] = useState<boolean | null>(null);

  // Validate token before redirecting
  useEffect(() => {
    const validateToken = async () => {
      try {
        // Only check if user exists
        if (user) {
          const token = await AsyncStorage.getItem('auth_token');

          if (token) {
            // Validate token
            const decodedToken = decodeJWT(token);

            // Check if token is invalid or expired
            if (!decodedToken ||
                (decodedToken.exp && decodedToken.exp < Math.floor(Date.now() / 1000))) {
              console.log('Auth layout: Token invalid or expired');
              setIsTokenValid(false);

              // Handle token expiration
              handleExpiredToken();
              return;
            }

            // Token is valid
            setIsTokenValid(true);
          } else {
            // No token found
            setIsTokenValid(false);
          }
        } else {
          // No user, token is not relevant
          setIsTokenValid(false);
        }
      } catch (error) {
        console.error('Error validating token in auth layout:', error);
        setIsTokenValid(false);
      }
    };

    validateToken();
  }, [user, handleExpiredToken]);

  // Show nothing while loading or validating token
  if (isLoading || isTokenValid === null) return null;

  // Only redirect if user exists AND token is valid
  if (user && isTokenValid) {
    console.log('Auth layout: User exists and token is valid, redirecting to home');
    return <Redirect href="/" />;
  }

  // Otherwise show login screen
  return <Slot />;
}
