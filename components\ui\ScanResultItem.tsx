import React from 'react';
import { StyleSheet, View, Text, TouchableOpacity } from 'react-native';
import { Barcode, ClipboardCheck, Package, Check, X } from 'lucide-react-native';
import { ScanResult } from '@/types';

interface ScanResultItemProps {
  result: ScanResult;
  onPress?: () => void;
}

export default function ScanResultItem({ result, onPress }: ScanResultItemProps) {
  const getIcon = () => {
    switch (result.type) {
      case 'general':
        return <Barcode size={20} color="#3b82f6" />;
      case 'order':
        return <ClipboardCheck size={20} color="#8b5cf6" />;
      case 'packet':
        return <Package size={20} color="#10b981" />;
      default:
        return <Barcode size={20} color="#3b82f6" />;
    }
  };

  const getTypeLabel = () => {
    switch (result.type) {
      case 'general':
        return 'General';
      case 'order':
        return 'Order';
      case 'packet':
        return 'Packet';
      default:
        return 'Unknown';
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString();
  };

  return (
    <TouchableOpacity 
      style={styles.container}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.iconContainer}>
        {getIcon()}
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.barcodeText} numberOfLines={1}>
            {result.barcode}
          </Text>
          
          <View style={[
            styles.statusBadge, 
            result.success ? styles.successBadge : styles.errorBadge
          ]}>
            {result.success ? 
              <Check size={12} color="#ffffff" /> : 
              <X size={12} color="#ffffff" />
            }
            <Text style={styles.statusText}>
              {result.success ? 'Success' : 'Failed'}
            </Text>
          </View>
        </View>
        
        <View style={styles.details}>
          <Text style={styles.typeLabel}>{getTypeLabel()}</Text>
          <Text style={styles.timestamp}>{formatDate(result.timestamp)}</Text>
        </View>
        
        {result.message && !result.success && (
          <Text style={styles.errorMessage}>{result.message}</Text>
        )}
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
    marginBottom: 8,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f1f5f9',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  barcodeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  successBadge: {
    backgroundColor: '#10b981',
  },
  errorBadge: {
    backgroundColor: '#ef4444',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#ffffff',
    marginLeft: 4,
  },
  details: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typeLabel: {
    fontSize: 14,
    color: '#64748b',
    marginRight: 8,
  },
  timestamp: {
    fontSize: 12,
    color: '#94a3b8',
  },
  errorMessage: {
    fontSize: 12,
    color: '#ef4444',
    marginTop: 4,
  },
});